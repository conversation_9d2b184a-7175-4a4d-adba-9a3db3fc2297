package org.cicd.utils

/**
 * 统一错误处理工具类
 * 提供标准化的错误处理和重试机制
 */
class ErrorHandler implements Serializable {
    
    /**
     * 执行带错误处理的操作
     * @param operation 要执行的操作闭包
     * @param operationName 操作名称，用于日志记录
     * @param maxRetries 最大重试次数，默认为0（不重试）
     * @param retryDelay 重试间隔（秒），默认为5秒
     */
    static def executeWithErrorHandling(Closure operation, String operationName, int maxRetries = 0, int retryDelay = 5) {
        def attempt = 0
        def lastException = null
        
        while (attempt <= maxRetries) {
            try {
                if (attempt > 0) {
                    Logger.info("重试操作 '${operationName}' (第${attempt}次重试)", "ErrorHandler")
                }
                
                return operation.call()
                
            } catch (Exception e) {
                lastException = e
                attempt++
                
                Logger.error("操作 '${operationName}' 失败 (尝试 ${attempt}/${maxRetries + 1}): ${e.message}", "ErrorHandler")
                
                if (attempt <= maxRetries) {
                    Logger.info("等待 ${retryDelay} 秒后重试...", "ErrorHandler")
                    sleep(retryDelay * 1000)
                } else {
                    Logger.error("操作 '${operationName}' 最终失败，已达到最大重试次数", "ErrorHandler")
                    throw new RuntimeException("操作 '${operationName}' 失败: ${e.message}", e)
                }
            }
        }
        
        // 这行代码理论上不会执行到，但为了代码完整性
        throw lastException
    }
    
    /**
     * 安全执行操作，失败时不抛出异常
     * @param operation 要执行的操作闭包
     * @param operationName 操作名称
     * @param defaultValue 失败时返回的默认值
     */
    static def safeExecute(Closure operation, String operationName, def defaultValue = null) {
        try {
            return operation.call()
        } catch (Exception e) {
            Logger.warning("操作 '${operationName}' 失败，返回默认值: ${e.message}", "ErrorHandler")
            return defaultValue
        }
    }
    
    /**
     * 验证前置条件
     * @param condition 条件表达式
     * @param errorMessage 条件不满足时的错误信息
     */
    static def requireCondition(boolean condition, String errorMessage) {
        if (!condition) {
            Logger.error("前置条件验证失败: ${errorMessage}", "ErrorHandler")
            throw new IllegalStateException(errorMessage)
        }
    }
    
    /**
     * 验证文件存在
     * @param filePath 文件路径
     * @param errorMessage 自定义错误信息
     */
    static def requireFileExists(String filePath, String errorMessage = null) {
        if (!fileExists(filePath)) {
            def message = errorMessage ?: "必需的文件不存在: ${filePath}"
            Logger.error(message, "ErrorHandler")
            throw new FileNotFoundException(message)
        }
    }
    
    /**
     * 验证环境变量存在
     * @param envVarName 环境变量名
     * @param envVars 环境变量映射（可选）
     * @param errorMessage 自定义错误信息
     */
    static def requireEnvVar(String envVarName, Map envVars = null, String errorMessage = null) {
        def value = envVars ? envVars[envVarName] : System.getenv(envVarName)
        if (!value) {
            def message = errorMessage ?: "必需的环境变量未设置: ${envVarName}"
            Logger.error(message, "ErrorHandler")
            throw new IllegalStateException(message)
        }
        return value
    }
    
    /**
     * 包装Shell命令执行，提供更好的错误信息
     * @param command Shell命令
     * @param operationName 操作名称
     * @param returnStdout 是否返回标准输出
     * @param allowFailure 是否允许命令失败
     */
    static def executeShell(String command, String operationName, boolean returnStdout = false, boolean allowFailure = false) {
        Logger.debug("执行Shell命令: ${command}", "ErrorHandler")

        try {
            def result = sh(script: command, returnStdout: returnStdout, returnStatus: allowFailure)

            if (allowFailure && result != 0) {
                Logger.warning("Shell命令执行失败但允许继续 (${operationName}): 退出码 ${result}", "ErrorHandler")
                return null
            }

            Logger.debug("Shell命令执行成功", "ErrorHandler")
            return returnStdout ? result : 0

        } catch (Exception e) {
            if (allowFailure) {
                Logger.warning("Shell命令执行失败但允许继续 (${operationName}): ${e.message}", "ErrorHandler")
                return null
            }

            Logger.error("Shell命令执行失败 (${operationName}): ${command}", "ErrorHandler")
            Logger.error("错误详情: ${e.message}", "ErrorHandler")

            // 提供更详细的错误信息
            def errorDetails = [
                command: command,
                operation: operationName,
                error: e.message,
                timestamp: new Date().toString()
            ]

            throw new RuntimeException("Shell命令执行失败 (${operationName}): ${e.message}", e)
        }
    }
}
