apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${appName}
  namespace: ${namespace}
  labels:
    app: ${appName}
  annotations:
    # HPA 自动扩缩容建议：副本数由 HPA 控制
spec:
  # replicas 字段由 HPA 控制，无需在此指定
  selector:
    matchLabels:
      app: ${appName}
  template:
    metadata:
      labels:
        app: ${appName}
      annotations:
        sidecar.istio.io/inject: "true"
    spec:
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - ${appName}
                topologyKey: "kubernetes.io/hostname"
      containers:
        - name: ${appName}
          image: ${imageName}:${tag}
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: ${port}
          env:
            - name: SPRING_PROFILES_ACTIVE
              value: "${env}"
            - name: JAVA_OPTS
              value: "${javaOpts}"
          resources:
            requests:
              memory: "512Mi"
              cpu: "500m"
            limits:
              memory: "2048Mi"
              cpu: "2000m"
          livenessProbe:
            httpGet:
              path: /actuator/health
              port: ${port}
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /actuator/health
              port: ${port}
            initialDelaySeconds: 10
            periodSeconds: 5
          volumeMounts:
            - name: app-config
              mountPath: /app/config
          command: ["sh", "-c"]
          args: ["java $JAVA_OPTS -jar /app/app.jar"]
      volumes:
        - name: app-config
          configMap:
            name: ${appName}-config
      imagePullSecrets:
        - name: regcred
