package org.cicd

import org.cicd.utils.Logger

/**
 * Go项目构建和部署工具类
 */
class DeployGo implements Serializable {

    /**
     * 构建Go项目
     */
    static def build(Map params) {
        Logger.stageStart("Go项目构建")

        try {
            Logger.info("整理Go模块依赖", "DeployGo")
            sh 'go mod tidy'

            Logger.info("构建Go项目", "DeployGo")
            sh 'go build .'

            Logger.success("Go项目构建完成", "DeployGo")
        } catch (Exception e) {
            Logger.error("Go项目构建失败: ${e.message}", "DeployGo")
            throw e
        } finally {
            Logger.stageEnd("Go项目构建")
        }
    }

    /**
     * 运行Go项目测试
     */
    static def test(Map params) {
        Logger.stageStart("Go项目测试")

        try {
            Logger.info("运行Go测试", "DeployGo")
            sh 'go test ./...'
            Logger.success("Go项目测试完成", "DeployGo")
        } catch (Exception e) {
            Logger.error("Go项目测试失败: ${e.message}", "DeployGo")
            throw e
        } finally {
            Logger.stageEnd("Go项目测试")
        }
    }

    /**
     * 部署Go项目
     */
    static def deploy(Map params) {
        Logger.info("Go项目部署逻辑可在此扩展", "DeployGo")
        // TODO: 实现具体的部署逻辑
    }
}