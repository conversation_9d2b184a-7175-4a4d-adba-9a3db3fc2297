package org.cicd

import org.cicd.utils.Logger
import org.cicd.utils.ErrorHandler
import org.cicd.security.SecurityUtils

/**
 * 通知工具类
 * 支持多种通知方式：钉钉、企业微信、邮件、Slack等
 */
class Notify implements Serializable {

    /**
     * 发送通知
     */
    static def send(Map config) {
        Logger.stageStart("发送通知")

        try {
            def notificationConfig = config.notification ?: [:]
            def message = buildNotificationMessage(config)

            // 发送到不同的通知渠道
            if (notificationConfig.dingtalk?.enabled) {
                sendDingTalkNotification(message, notificationConfig.dingtalk)
            }

            if (notificationConfig.wechat?.enabled) {
                sendWeChatNotification(message, notificationConfig.wechat)
            }

            if (notificationConfig.email?.enabled) {
                sendEmailNotification(message, notificationConfig.email)
            }

            if (notificationConfig.slack?.enabled) {
                sendSlackNotification(message, notificationConfig.slack)
            }

            // 默认控制台输出
            Logger.info("构建通知: ${SecurityUtils.maskSensitiveInfo(message)}", "Notify")

        } catch (Exception e) {
            Logger.error("发送通知失败: ${e.message}", "Notify")
            // 通知失败不应该影响主流程
        } finally {
            Logger.stageEnd("发送通知")
        }
    }

    /**
     * 构建通知消息
     */
    private static def buildNotificationMessage(Map config) {
        def status = currentBuild.result ?: 'SUCCESS'
        def duration = currentBuild.durationString
        def buildUrl = env.BUILD_URL

        def message = """
📋 **Jenkins构建通知**
🏷️ **项目**: ${config.JOB_NAME}
🌍 **环境**: ${config.ENV}
🚀 **部署方式**: ${config.DEPLOY_TYPE ?: '未指定'}
🏷️ **版本标签**: ${config.TAG}
🌿 **分支**: ${config.BRANCH_NAME}
📊 **状态**: ${status}
⏱️ **耗时**: ${duration}
🔗 **构建链接**: ${buildUrl}
""".trim()

        return message
    }

    /**
     * 发送钉钉通知
     */
    private static def sendDingTalkNotification(String message, Map config) {
        try {
            Logger.info("发送钉钉通知", "Notify")

            def webhook = SecurityUtils.getCredentials(config.webhookCredentialsId, 'string')
            def payload = [
                msgtype: "markdown",
                markdown: [
                    title: "Jenkins构建通知",
                    text: message
                ]
            ]

            def response = httpRequest(
                httpMode: 'POST',
                url: webhook,
                contentType: 'APPLICATION_JSON',
                requestBody: writeJSON(returnText: true, json: payload)
            )

            Logger.success("钉钉通知发送成功", "Notify")

        } catch (Exception e) {
            Logger.error("钉钉通知发送失败: ${e.message}", "Notify")
        }
    }

    /**
     * 发送企业微信通知
     */
    private static def sendWeChatNotification(String message, Map config) {
        try {
            Logger.info("发送企业微信通知", "Notify")

            def webhook = SecurityUtils.getCredentials(config.webhookCredentialsId, 'string')
            def payload = [
                msgtype: "markdown",
                markdown: [
                    content: message
                ]
            ]

            def response = httpRequest(
                httpMode: 'POST',
                url: webhook,
                contentType: 'APPLICATION_JSON',
                requestBody: writeJSON(returnText: true, json: payload)
            )

            Logger.success("企业微信通知发送成功", "Notify")

        } catch (Exception e) {
            Logger.error("企业微信通知发送失败: ${e.message}", "Notify")
        }
    }

    /**
     * 发送邮件通知
     */
    private static def sendEmailNotification(String message, Map config) {
        try {
            Logger.info("发送邮件通知", "Notify")

            emailext(
                subject: "Jenkins构建通知 - ${config.JOB_NAME} - ${currentBuild.result ?: 'SUCCESS'}",
                body: message,
                to: config.recipients,
                mimeType: 'text/plain'
            )

            Logger.success("邮件通知发送成功", "Notify")

        } catch (Exception e) {
            Logger.error("邮件通知发送失败: ${e.message}", "Notify")
        }
    }

    /**
     * 发送Slack通知
     */
    private static def sendSlackNotification(String message, Map config) {
        try {
            Logger.info("发送Slack通知", "Notify")

            def webhook = SecurityUtils.getCredentials(config.webhookCredentialsId, 'string')
            def payload = [
                text: message,
                channel: config.channel ?: '#general',
                username: 'Jenkins',
                icon_emoji: ':jenkins:'
            ]

            def response = httpRequest(
                httpMode: 'POST',
                url: webhook,
                contentType: 'APPLICATION_JSON',
                requestBody: writeJSON(returnText: true, json: payload)
            )

            Logger.success("Slack通知发送成功", "Notify")

        } catch (Exception e) {
            Logger.error("Slack通知发送失败: ${e.message}", "Notify")
        }
    }
}