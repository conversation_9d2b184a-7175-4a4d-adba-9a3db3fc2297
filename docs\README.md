# Jenkins 通用 Pipeline 共享库 v2.0

## 🚀 功能特性

### 核心功能
- ✅ **多环境支持**：dev/test/prod/gray 环境参数化部署
- ✅ **多语言支持**：Java、Vue、Go 项目自动识别与构建
- ✅ **多部署方式**：Docker、Docker Compose、Kubernetes
- ✅ **灰度发布**：支持灰度发布与一键回滚
- ✅ **自动标签**：格式 `vyyyyMMddHHmmss_BUILD_NUMBER`
- ✅ **模板系统**：自动处理 Dockerfile 和 K8s 配置模板
- ✅ **多渠道通知**：钉钉、企业微信、邮件、Slack

### 新增优化功能 🆕
- 🔧 **统一配置管理**：集中化的配置处理和验证
- 📝 **结构化日志**：彩色输出、分级日志、调试信息
- 🛡️ **安全增强**：输入验证、凭据管理、安全检查
- 🔄 **错误处理**：自动重试、详细错误信息、故障恢复
- 📋 **模板处理**：自动变量替换、多项目类型支持
- ✅ **参数验证**：全面的输入验证和安全检查

## 目录结构
```
jenkinslib2/
  ├── vars/
  │   ├── buildAndDeploy.groovy      # 主入口
  │   ├── envConfig.groovy           # 环境变量替换
  │   ├── rollback.groovy            # 回滚逻辑
  │   ├── grayRelease.groovy         # 灰度发布逻辑
  ├── src/org/cicd/devops/
  │   ├── build.groovy               # 构建逻辑
  │   └── deploy.groovy              # 部署逻辑
  ├── src/org/cicd/utils/
  │   └── utils.groovy               # 通知等工具
  └── docs/README.md
```

## 📖 快速开始

### 1. 基本使用

```groovy
@Library('jenkinslib2@main') _

buildAndDeploy(
    repoUrl: '**************:company/project.git',
    imageName: 'registry.company.com/project/app',
    containerName: 'app',
    k8sNamespace: 'production',
    appName: 'my-app',
    port: 8080
)
```

### 2. 完整配置示例

```groovy
@Library('jenkinslib2@main') _

buildAndDeploy(
    // === 基本配置 ===
    repoUrl: '**************:company/project.git',
    credentialsId: 'git-ssh-key',

    // === 镜像配置 ===
    imageName: 'registry.company.com/project/app',
    containerName: 'app',

    // === K8s配置 ===
    k8sNamespace: 'production',
    appName: 'my-app',
    port: 8080,
    replicas: 3,

    // === Java项目特定配置 ===
    javaOpts: '-Xms1g -Xmx2g -Dspring.profiles.active=prod',

    // === 通知配置 ===
    notification: [
        dingtalk: [
            enabled: true,
            webhookCredentialsId: 'dingtalk-webhook'
        ],
        email: [
            enabled: true,
            recipients: '<EMAIL>,<EMAIL>'
        ]
    ],

    // === 自定义环境变量 ===
    envVars: [
        'JAVA_TOOL_OPTIONS': '-Dfile.encoding=UTF-8',
        'TZ': 'Asia/Shanghai',
        'LOG_LEVEL': 'INFO'
    ]
)
```

## 📋 参数说明

### 必需参数
| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| `repoUrl` | String | Git仓库地址 | `**************:user/repo.git` |
| `imageName` | String | Docker镜像名称 | `registry.com/namespace/app` |

### 可选参数
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `credentialsId` | String | `null` | Git凭据ID |
| `containerName` | String | `app` | Docker容器名称 |
| `k8sNamespace` | String | `default` | K8s命名空间 |
| `appName` | String | `app` | K8s应用名称 |
| `port` | Integer | `8080` | 应用端口 |
| `replicas` | Integer | `2` | K8s副本数 |
| `javaOpts` | String | `-Xms512m -Xmx1024m` | Java JVM参数 |
| `notification` | Map | `[:]` | 通知配置 |
| `envVars` | Map | `[:]` | 自定义环境变量 |

### Pipeline参数
这些参数在Jenkins Pipeline中通过UI选择：

| 参数 | 类型 | 选项 | 说明 |
|------|------|------|------|
| `ENV` | Choice | `dev`, `test`, `prod`, `gray` | 部署环境 |
| `BRANCH_NAME` | String | `main` | 构建分支 |
| `DEPLOY_TYPE` | Choice | `docker`, `docker-compose`, `k8s` | 部署方式 |
| `GRAY_RELEASE` | Boolean | `false` | 是否灰度发布 |
| `ROLLBACK` | Boolean | `false` | 是否回滚 |
| `SKIP_TESTS` | Boolean | `false` | 是否跳过测试 |

## 🏷️ 标签规则

自动生成格式：`vyyyyMMddHHmmss_BUILD_NUMBER`

示例：`v20241210143022_123`
- `v`: 版本前缀
- `20241210143022`: 时间戳 (年月日时分秒)
- `123`: Jenkins构建号

## 🔧 模板系统

### 支持的模板类型

#### Dockerfile模板
- `dockerfile-java.vm`: Java项目Dockerfile
- `dockerfile-vue.vm`: Vue项目Dockerfile
- `dockerfile-go.vm`: Go项目Dockerfile

#### K8s部署模板
- `k8s-deployment-java.yml.vm`: Java应用K8s部署配置
- `k8s-deployment-vue.yml.vm`: Vue应用K8s部署配置
- `k8s-deployment-go.yml.vm`: Go应用K8s部署配置

### 模板变量

模板中可以使用以下变量：

| 变量 | 说明 | 示例 |
|------|------|------|
| `${appName}` | 应用名称 | `my-app` |
| `${namespace}` | K8s命名空间 | `production` |
| `${imageName}` | 镜像名称 | `registry.com/app` |
| `${tag}` | 镜像标签 | `v20241210143022_123` |
| `${port}` | 应用端口 | `8080` |
| `${replicas}` | 副本数 | `3` |
| `${env}` | 环境名称 | `prod` |
| `${javaOpts}` | Java JVM参数 | `-Xms1g -Xmx2g` |

### 自定义模板

1. 在 `resources/templates/` 目录下创建 `.vm` 文件
2. 使用 `${变量名}` 格式定义变量
3. 模板会自动被处理并生成最终文件

## 📢 通知配置

### 钉钉通知

```groovy
notification: [
    dingtalk: [
        enabled: true,
        webhookCredentialsId: 'dingtalk-webhook-secret'
    ]
]
```

### 企业微信通知

```groovy
notification: [
    wechat: [
        enabled: true,
        webhookCredentialsId: 'wechat-webhook-secret'
    ]
]
```

### 邮件通知

```groovy
notification: [
    email: [
        enabled: true,
        recipients: '<EMAIL>,<EMAIL>'
    ]
]
```

### Slack通知

```groovy
notification: [
    slack: [
        enabled: true,
        webhookCredentialsId: 'slack-webhook-secret',
        channel: '#deployments'
    ]
]
```

## 🛡️ 安全特性

### 输入验证
- 自动验证所有输入参数
- 防止Shell注入攻击
- 检查危险命令和字符

### 凭据管理
- 安全地获取和使用Jenkins凭据
- 自动掩码敏感信息
- 支持多种凭据类型

### 文件权限检查
- 检查文件权限设置
- 警告不安全的权限配置
- 自动清理临时文件

## 🔄 错误处理

### 自动重试
- 网络操作自动重试
- 可配置重试次数和间隔
- 智能故障恢复

### 详细日志
- 结构化日志输出
- 彩色日志显示
- 分级日志记录

### 故障诊断
- 自动收集错误信息
- 提供详细的故障上下文
- K8s Pod日志自动获取