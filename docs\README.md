# Jenkins 通用 Pipeline 共享库

## 功能特性
- 支持多环境（dev/test/prod/gray）参数化部署
- 支持分支选择构建
- 支持 Java、Vue、Go 多语言项目自动识别与构建
- 支持 docker、docker-compose、k8s 多种部署方式
- 支持灰度发布与一键回滚
- 自动生成 tag，格式为：`vyyyyMMddHHmmss_BUILD_NUMBER`
- 支持统一通知（可扩展钉钉、企业微信、邮件等）

## 目录结构
```
jenkinslib2/
  ├── vars/
  │   ├── buildAndDeploy.groovy      # 主入口
  │   ├── envConfig.groovy           # 环境变量替换
  │   ├── rollback.groovy            # 回滚逻辑
  │   ├── grayRelease.groovy         # 灰度发布逻辑
  ├── src/org/cicd/devops/
  │   ├── build.groovy               # 构建逻辑
  │   └── deploy.groovy              # 部署逻辑
  ├── src/org/cicd/utils/
  │   └── utils.groovy               # 通知等工具
  └── docs/README.md
```

## Jenkinsfile 调用示例
```groovy
@Library('jenkinslib2@old') _

buildAndDeploy(
    repoUrl: '***********',
    imageName: 'myapp',
    containerName: 'myapp',
    k8sNamespace: 'default'
)
```

## 参数说明
- ENV：部署环境（dev/test/prod/gray）
- BRANCH_NAME：构建分支
- DEPLOY_TYPE：部署方式（docker/docker-compose/k8s）
- GRAY_RELEASE：是否灰度发布
- ROLLBACK：是否回滚
- imageName/containerName/k8sNamespace：镜像、容器、k8s命名空间等

## Tag 规则
自动生成格式：`vyyyyMMddHHmmss_BUILD_NUMBER`

## 扩展
- 可根据实际需求扩展通知、构建、部署等逻辑 