package org.cicd.config

import org.cicd.utils.Logger
import org.cicd.utils.ErrorHandler
import org.cicd.utils.ConfigValidator

/**
 * 统一配置管理器
 * 提供环境配置、参数验证和配置文件处理功能
 */
class ConfigManager implements Serializable {
    
    // 默认配置
    private static final Map DEFAULT_CONFIG = [
        environments: ['dev', 'test', 'prod', 'gray'],
        deployTypes: ['docker', 'docker-compose', 'k8s'],
        defaultNamespace: 'default',
        defaultPort: 8080,
        defaultReplicas: 2,
        tagFormat: 'v{timestamp}_{buildNumber}',
        retryAttempts: 2,
        retryDelay: 5
    ]
    
    /**
     * 获取完整的配置对象
     */
    static def getConfig(Map userParams = [:]) {
        Logger.debug("开始构建配置对象", "ConfigManager")
        
        def config = [:]
        
        // 合并默认配置
        config.putAll(DEFAULT_CONFIG)
        
        // 合并用户参数
        config.putAll(userParams)
        
        // 处理环境变量
        processEnvironmentVariables(config)
        
        // 生成标签
        generateTag(config)
        
        // 验证配置
        validateConfig(config)
        
        Logger.debug("配置对象构建完成", "ConfigManager")
        return config
    }
    
    /**
     * 处理环境变量
     */
    private static def processEnvironmentVariables(Map config) {
        // 从Jenkins环境变量获取值
        config.ENV = config.ENV ?: env.ENV ?: 'dev'
        config.BRANCH_NAME = config.BRANCH_NAME ?: env.BRANCH_NAME ?: 'main'
        config.BUILD_NUMBER = config.BUILD_NUMBER ?: env.BUILD_NUMBER ?: '1'
        config.JOB_NAME = config.JOB_NAME ?: env.JOB_NAME ?: 'unknown'
        config.WORKSPACE = config.WORKSPACE ?: env.WORKSPACE ?: pwd()
        
        // 设置默认值
        config.k8sNamespace = config.k8sNamespace ?: config.defaultNamespace
        config.port = config.port ?: config.defaultPort
        config.replicas = config.replicas ?: config.defaultReplicas
    }
    
    /**
     * 生成版本标签
     */
    private static def generateTag(Map config) {
        if (!config.TAG) {
            def timestamp = new Date().format('yyyyMMddHHmmss')
            config.TAG = "v${timestamp}_${config.BUILD_NUMBER}"
        }
    }
    
    /**
     * 验证配置
     */
    private static def validateConfig(Map config) {
        Logger.debug("开始验证配置", "ConfigManager")
        
        // 验证环境
        ConfigValidator.validateEnvironment(config.ENV)
        
        // 验证部署类型
        if (config.DEPLOY_TYPE) {
            ConfigValidator.validateDeployType(config.DEPLOY_TYPE)
        }
        
        // 验证镜像名称
        if (config.imageName) {
            ConfigValidator.validateImageName(config.imageName)
        }
        
        // 验证容器名称
        if (config.containerName) {
            ConfigValidator.validateContainerName(config.containerName)
        }
        
        // 验证命名空间
        if (config.k8sNamespace) {
            ConfigValidator.validateNamespace(config.k8sNamespace)
        }
        
        // 验证端口
        if (config.port) {
            ConfigValidator.validatePort(config.port)
        }
        
        // 验证Git URL
        if (config.repoUrl) {
            ConfigValidator.validateGitUrl(config.repoUrl)
        }
        
        Logger.debug("配置验证完成", "ConfigManager")
    }
    
    /**
     * 替换配置文件中的环境变量
     */
    static def replaceConfigFiles(Map config) {
        Logger.stageStart("配置文件替换")
        
        try {
            def env = config.ENV
            Logger.info("替换配置文件中的环境变量: ${env}", "ConfigManager")
            
            // Spring Boot应用配置
            replaceSpringConfig(env)
            
            // Node.js应用配置
            replaceNodeConfig(env)
            
            // Go应用配置
            replaceGoConfig(env)
            
            // 通用环境文件
            replaceEnvFile(env)
            
            Logger.success("配置文件替换完成", "ConfigManager")
            
        } catch (Exception e) {
            Logger.error("配置文件替换失败: ${e.message}", "ConfigManager")
            throw e
        } finally {
            Logger.stageEnd("配置文件替换")
        }
    }
    
    /**
     * 替换Spring Boot配置
     */
    private static def replaceSpringConfig(String env) {
        def configFiles = ['application.yml', 'application.yaml', 'application.properties']
        
        configFiles.each { fileName ->
            if (fileExists(fileName)) {
                Logger.info("替换Spring配置文件: ${fileName}", "ConfigManager")
                
                if (fileName.endsWith('.properties')) {
                    ErrorHandler.executeShell("sed -i 's/^spring.profiles.active=.*/spring.profiles.active=${env}/' ${fileName}", "替换Spring配置")
                } else {
                    ErrorHandler.executeShell("sed -i 's/^spring.profiles.active:.*/spring.profiles.active: ${env}/' ${fileName}", "替换Spring配置")
                }
            }
        }
    }
    
    /**
     * 替换Node.js配置
     */
    private static def replaceNodeConfig(String env) {
        def configFiles = ['src/config.js', 'config/config.js', '.env.local']
        
        configFiles.each { fileName ->
            if (fileExists(fileName)) {
                Logger.info("替换Node.js配置文件: ${fileName}", "ConfigManager")
                
                if (fileName.endsWith('.js')) {
                    ErrorHandler.executeShell("sed -i 's/ENV:.*/ENV: \"${env}\",/' ${fileName}", "替换Node.js配置")
                } else {
                    ErrorHandler.executeShell("sed -i 's/^NODE_ENV=.*/NODE_ENV=${env}/' ${fileName}", "替换Node.js配置")
                }
            }
        }
    }
    
    /**
     * 替换Go配置
     */
    private static def replaceGoConfig(String env) {
        if (fileExists('config.yaml') || fileExists('config.yml')) {
            def configFile = fileExists('config.yaml') ? 'config.yaml' : 'config.yml'
            Logger.info("替换Go配置文件: ${configFile}", "ConfigManager")
            ErrorHandler.executeShell("sed -i 's/^env:.*/env: ${env}/' ${configFile}", "替换Go配置")
        }
    }
    
    /**
     * 替换环境文件
     */
    private static def replaceEnvFile(String env) {
        if (fileExists('.env')) {
            Logger.info("替换环境文件: .env", "ConfigManager")
            ErrorHandler.executeShell("sed -i 's/^ENV=.*/ENV=${env}/' .env", "替换环境文件")
        }
    }
    
    /**
     * 获取项目类型
     */
    static def detectProjectType() {
        if (fileExists('pom.xml')) {
            return 'java'
        } else if (fileExists('package.json')) {
            return 'vue'
        } else if (fileExists('go.mod')) {
            return 'go'
        } else {
            throw new IllegalStateException("无法识别项目类型，请确保项目根目录包含 pom.xml、package.json 或 go.mod 文件")
        }
    }
    
    /**
     * 打印配置信息
     */
    static def printConfig(Map config) {
        Logger.info("=== 当前配置信息 ===", "ConfigManager")
        Logger.info("环境: ${config.ENV}", "ConfigManager")
        Logger.info("分支: ${config.BRANCH_NAME}", "ConfigManager")
        Logger.info("标签: ${config.TAG}", "ConfigManager")
        Logger.info("部署类型: ${config.DEPLOY_TYPE ?: '未指定'}", "ConfigManager")
        Logger.info("镜像名称: ${config.imageName ?: '未指定'}", "ConfigManager")
        Logger.info("容器名称: ${config.containerName ?: '未指定'}", "ConfigManager")
        Logger.info("K8s命名空间: ${config.k8sNamespace ?: '未指定'}", "ConfigManager")
        Logger.info("端口: ${config.port ?: '未指定'}", "ConfigManager")
        Logger.info("==================", "ConfigManager")
    }
}
