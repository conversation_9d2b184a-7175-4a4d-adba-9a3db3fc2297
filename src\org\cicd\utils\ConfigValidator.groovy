package org.cicd.utils

/**
 * 配置验证工具类
 * 用于验证输入参数和配置的有效性
 */
class ConfigValidator implements Serializable {
    
    /**
     * 验证必需参数
     */
    static def validateRequiredParams(Map params, List<String> requiredKeys) {
        def missingKeys = []
        requiredKeys.each { key ->
            if (!params.containsKey(key) || !params[key]) {
                missingKeys.add(key)
            }
        }
        
        if (missingKeys) {
            throw new IllegalArgumentException("缺少必需参数: ${missingKeys.join(', ')}")
        }
    }
    
    /**
     * 验证环境参数
     */
    static def validateEnvironment(String env) {
        def validEnvs = ['dev', 'test', 'prod', 'gray']
        if (!validEnvs.contains(env)) {
            throw new IllegalArgumentException("无效的环境参数: ${env}. 有效值: ${validEnvs.join(', ')}")
        }
    }
    
    /**
     * 验证部署类型
     */
    static def validateDeployType(String deployType) {
        def validTypes = ['docker', 'docker-compose', 'k8s']
        if (!validTypes.contains(deployType)) {
            throw new IllegalArgumentException("无效的部署类型: ${deployType}. 有效值: ${validTypes.join(', ')}")
        }
    }
    
    /**
     * 验证镜像名称格式
     */
    static def validateImageName(String imageName) {
        if (!imageName || !imageName.matches(/^[a-z0-9]+([\.\-\/][a-z0-9]+)*$/)) {
            throw new IllegalArgumentException("无效的镜像名称格式: ${imageName}")
        }
    }
    
    /**
     * 验证容器名称格式
     */
    static def validateContainerName(String containerName) {
        if (!containerName || !containerName.matches(/^[a-zA-Z0-9][a-zA-Z0-9_.-]*$/)) {
            throw new IllegalArgumentException("无效的容器名称格式: ${containerName}")
        }
    }
    
    /**
     * 验证K8s命名空间格式
     */
    static def validateNamespace(String namespace) {
        if (!namespace || !namespace.matches(/^[a-z0-9]([-a-z0-9]*[a-z0-9])?$/)) {
            throw new IllegalArgumentException("无效的K8s命名空间格式: ${namespace}")
        }
    }
    
    /**
     * 验证端口号
     */
    static def validatePort(def port) {
        def portNum = port as Integer
        if (portNum < 1 || portNum > 65535) {
            throw new IllegalArgumentException("无效的端口号: ${port}. 有效范围: 1-65535")
        }
    }
    
    /**
     * 验证Git URL格式
     */
    static def validateGitUrl(String gitUrl) {
        if (!gitUrl || !(gitUrl.startsWith('http') || gitUrl.startsWith('git@'))) {
            throw new IllegalArgumentException("无效的Git URL格式: ${gitUrl}")
        }
    }
}
