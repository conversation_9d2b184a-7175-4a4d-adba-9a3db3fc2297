package org.cicd.utils

/**
 * 全局异常处理器
 * 提供统一的异常处理和恢复策略
 */
class ExceptionHandler implements Serializable {
    
    /**
     * 处理构建异常
     * @param e 异常对象
     * @param stage 发生异常的阶段
     * @param config 配置对象
     */
    static def handleBuildException(Exception e, String stage, Map config = [:]) {
        Logger.error("构建阶段 '${stage}' 发生异常", "ExceptionHandler")
        Logger.error("异常类型: ${e.class.simpleName}", "ExceptionHandler")
        Logger.error("异常信息: ${e.message}", "ExceptionHandler")
        
        // 记录异常详情
        def exceptionDetails = [
            stage: stage,
            exception: e.class.simpleName,
            message: e.message,
            timestamp: new Date().toString(),
            buildNumber: config.BUILD_NUMBER ?: 'unknown',
            environment: config.ENV ?: 'unknown'
        ]
        
        // 根据异常类型提供恢复建议
        def suggestions = getRecoverySuggestions(e, stage)
        if (suggestions) {
            Logger.info("恢复建议:", "ExceptionHandler")
            suggestions.each { suggestion ->
                Logger.info("  - ${suggestion}", "ExceptionHandler")
            }
        }
        
        // 尝试清理资源
        cleanupResources(stage, config)
        
        // 重新抛出异常
        throw new RuntimeException("构建失败在阶段 '${stage}': ${e.message}", e)
    }
    
    /**
     * 处理部署异常
     * @param e 异常对象
     * @param deployType 部署类型
     * @param config 配置对象
     */
    static def handleDeployException(Exception e, String deployType, Map config = [:]) {
        Logger.error("部署异常 (${deployType})", "ExceptionHandler")
        Logger.error("异常信息: ${e.message}", "ExceptionHandler")
        
        // 根据部署类型进行特定处理
        switch (deployType) {
            case 'docker':
                handleDockerException(e, config)
                break
            case 'k8s':
                handleK8sException(e, config)
                break
            case 'docker-compose':
                handleDockerComposeException(e, config)
                break
            default:
                Logger.warning("未知的部署类型: ${deployType}", "ExceptionHandler")
        }
        
        throw new RuntimeException("部署失败 (${deployType}): ${e.message}", e)
    }
    
    /**
     * 获取恢复建议
     * @param e 异常对象
     * @param stage 阶段名称
     * @return 建议列表
     */
    private static def getRecoverySuggestions(Exception e, String stage) {
        def suggestions = []
        
        def errorMessage = e.message?.toLowerCase() ?: ""
        
        // 网络相关错误
        if (errorMessage.contains("network") || errorMessage.contains("timeout") || errorMessage.contains("connection")) {
            suggestions.add("检查网络连接")
            suggestions.add("重试操作")
            suggestions.add("检查防火墙设置")
        }
        
        // 权限相关错误
        if (errorMessage.contains("permission") || errorMessage.contains("access denied") || errorMessage.contains("forbidden")) {
            suggestions.add("检查文件权限")
            suggestions.add("验证用户权限")
            suggestions.add("检查凭据配置")
        }
        
        // 磁盘空间错误
        if (errorMessage.contains("no space") || errorMessage.contains("disk full")) {
            suggestions.add("清理磁盘空间")
            suggestions.add("检查临时文件")
            suggestions.add("清理Docker镜像和容器")
        }
        
        // Docker相关错误
        if (errorMessage.contains("docker") || errorMessage.contains("container")) {
            suggestions.add("检查Docker服务状态")
            suggestions.add("验证Docker镜像")
            suggestions.add("检查容器资源限制")
        }
        
        // K8s相关错误
        if (errorMessage.contains("kubernetes") || errorMessage.contains("kubectl")) {
            suggestions.add("检查K8s集群连接")
            suggestions.add("验证kubectl配置")
            suggestions.add("检查命名空间权限")
        }
        
        // 构建相关错误
        if (stage == "构建" || stage == "build") {
            suggestions.add("检查依赖配置")
            suggestions.add("清理构建缓存")
            suggestions.add("验证构建工具版本")
        }
        
        return suggestions
    }
    
    /**
     * 处理Docker异常
     */
    private static def handleDockerException(Exception e, Map config) {
        Logger.info("尝试Docker异常恢复", "ExceptionHandler")
        
        // 尝试清理失败的容器
        ErrorHandler.safeExecute({
            ErrorHandler.executeShell("docker ps -a --filter 'status=exited' -q | xargs -r docker rm", "清理退出的容器", false, true)
        }, "清理Docker容器")
        
        // 尝试清理悬空镜像
        ErrorHandler.safeExecute({
            ErrorHandler.executeShell("docker image prune -f", "清理悬空镜像", false, true)
        }, "清理Docker镜像")
    }
    
    /**
     * 处理K8s异常
     */
    private static def handleK8sException(Exception e, Map config) {
        Logger.info("尝试K8s异常恢复", "ExceptionHandler")
        
        def namespace = config.k8sNamespace ?: 'default'
        def appName = config.appName ?: 'app'
        
        // 获取Pod状态
        ErrorHandler.safeExecute({
            def podStatus = ErrorHandler.executeShell("kubectl get pods -l app=${appName} -n ${namespace}", "获取Pod状态", true, true)
            Logger.info("当前Pod状态:\n${podStatus}", "ExceptionHandler")
        }, "获取K8s Pod状态")
        
        // 获取事件信息
        ErrorHandler.safeExecute({
            def events = ErrorHandler.executeShell("kubectl get events -n ${namespace} --sort-by='.lastTimestamp' | tail -10", "获取K8s事件", true, true)
            Logger.info("最近的K8s事件:\n${events}", "ExceptionHandler")
        }, "获取K8s事件")
    }
    
    /**
     * 处理Docker Compose异常
     */
    private static def handleDockerComposeException(Exception e, Map config) {
        Logger.info("尝试Docker Compose异常恢复", "ExceptionHandler")
        
        // 获取服务状态
        ErrorHandler.safeExecute({
            def status = ErrorHandler.executeShell("docker-compose ps", "获取Compose服务状态", true, true)
            Logger.info("Docker Compose服务状态:\n${status}", "ExceptionHandler")
        }, "获取Docker Compose状态")
        
        // 获取日志
        ErrorHandler.safeExecute({
            def logs = ErrorHandler.executeShell("docker-compose logs --tail=20", "获取Compose日志", true, true)
            Logger.info("Docker Compose日志:\n${logs}", "ExceptionHandler")
        }, "获取Docker Compose日志")
    }
    
    /**
     * 清理资源
     * @param stage 阶段名称
     * @param config 配置对象
     */
    private static def cleanupResources(String stage, Map config) {
        Logger.info("开始清理资源", "ExceptionHandler")
        
        // 清理临时文件
        ErrorHandler.safeExecute({
            ErrorHandler.executeShell("find . -name '*.tmp' -type f -delete", "清理临时文件", false, true)
        }, "清理临时文件")
        
        // 清理构建缓存（如果是构建阶段失败）
        if (stage.contains("构建") || stage.contains("build")) {
            // Maven缓存清理
            ErrorHandler.safeExecute({
                ErrorHandler.executeShell("mvn clean", "清理Maven缓存", false, true)
            }, "清理Maven缓存")
            
            // NPM缓存清理
            ErrorHandler.safeExecute({
                ErrorHandler.executeShell("npm cache clean --force", "清理NPM缓存", false, true)
            }, "清理NPM缓存")
        }
        
        Logger.info("资源清理完成", "ExceptionHandler")
    }
}
