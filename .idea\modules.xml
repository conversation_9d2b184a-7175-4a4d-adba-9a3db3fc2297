<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ProjectModuleManager">
    <modules>
      <module fileurl="file://$PROJECT_DIR$/resources/app-backend-gateway/app-backend-gateway.iml" filepath="$PROJECT_DIR$/resources/app-backend-gateway/app-backend-gateway.iml" />
      <module fileurl="file://$PROJECT_DIR$/resources/cube-academy-service/cube-academy-service.iml" filepath="$PROJECT_DIR$/resources/cube-academy-service/cube-academy-service.iml" />
      <module fileurl="file://$PROJECT_DIR$/resources/cube-baike-server/cube-baike-server.iml" filepath="$PROJECT_DIR$/resources/cube-baike-server/cube-baike-server.iml" />
      <module fileurl="file://$PROJECT_DIR$/resources/cube-battle-service/cube-battle-service.iml" filepath="$PROJECT_DIR$/resources/cube-battle-service/cube-battle-service.iml" />
      <module fileurl="file://$PROJECT_DIR$/resources/cube-common-service/cube-common-service.iml" filepath="$PROJECT_DIR$/resources/cube-common-service/cube-common-service.iml" />
      <module fileurl="file://$PROJECT_DIR$/resources/cube-ketang-admin/cube-ketang-admin.iml" filepath="$PROJECT_DIR$/resources/cube-ketang-admin/cube-ketang-admin.iml" />
      <module fileurl="file://$PROJECT_DIR$/resources/cube-ketang-h5/cube-ketang-h5.iml" filepath="$PROJECT_DIR$/resources/cube-ketang-h5/cube-ketang-h5.iml" />
      <module fileurl="file://$PROJECT_DIR$/resources/cube-ketang-server/cube-ketang-server.iml" filepath="$PROJECT_DIR$/resources/cube-ketang-server/cube-ketang-server.iml" />
      <module fileurl="file://$PROJECT_DIR$/resources/cube-push-service/cube-push-service.iml" filepath="$PROJECT_DIR$/resources/cube-push-service/cube-push-service.iml" />
      <module fileurl="file://$PROJECT_DIR$/resources/cube-race-service/cube-race-service.iml" filepath="$PROJECT_DIR$/resources/cube-race-service/cube-race-service.iml" />
      <module fileurl="file://$PROJECT_DIR$/resources/cube-rank-service/cube-rank-service.iml" filepath="$PROJECT_DIR$/resources/cube-rank-service/cube-rank-service.iml" />
      <module fileurl="file://$PROJECT_DIR$/resources/cube-review-service/cube-review-service.iml" filepath="$PROJECT_DIR$/resources/cube-review-service/cube-review-service.iml" />
      <module fileurl="file://$PROJECT_DIR$/resources/cube-time-trial-service/cube-time-trial-service.iml" filepath="$PROJECT_DIR$/resources/cube-time-trial-service/cube-time-trial-service.iml" />
      <module fileurl="file://$PROJECT_DIR$/resources/cube-toutiao-server/cube-toutiao-server.iml" filepath="$PROJECT_DIR$/resources/cube-toutiao-server/cube-toutiao-server.iml" />
      <module fileurl="file://$PROJECT_DIR$/resources/cube-video-server/cube-video-server.iml" filepath="$PROJECT_DIR$/resources/cube-video-server/cube-video-server.iml" />
      <module fileurl="file://$PROJECT_DIR$/resources/data-center-service/data-center-service.iml" filepath="$PROJECT_DIR$/resources/data-center-service/data-center-service.iml" />
      <module fileurl="file://$PROJECT_DIR$/resources/go-socket-server/go-socket-server.iml" filepath="$PROJECT_DIR$/resources/go-socket-server/go-socket-server.iml" />
      <module fileurl="file://$PROJECT_DIR$/jenkinslib.iml" filepath="$PROJECT_DIR$/jenkinslib.iml" />
      <module fileurl="file://$PROJECT_DIR$/resources/log-service/log-service.iml" filepath="$PROJECT_DIR$/resources/log-service/log-service.iml" />
      <module fileurl="file://$PROJECT_DIR$/resources/resources.iml" filepath="$PROJECT_DIR$/resources/resources.iml" />
      <module fileurl="file://$PROJECT_DIR$/resources/user-center-service/user-center-service.iml" filepath="$PROJECT_DIR$/resources/user-center-service/user-center-service.iml" />
      <module fileurl="file://$PROJECT_DIR$/resources/user-global-service/user-global-service.iml" filepath="$PROJECT_DIR$/resources/user-global-service/user-global-service.iml" />
    </modules>
  </component>
</project>