def call(Map params = [:]) {
    pipeline {
        agent any
        parameters {
            choice(name: 'ENV', choices: ['dev', 'test', 'prod', 'gray'], description: '部署环境')
            string(name: 'BRANCH_NAME', defaultValue: 'main', description: '构建分支')
            choice(name: 'DEPLOY_TYPE', choices: ['docker', 'docker-compose', 'k8s'], description: '部署方式')
            booleanParam(name: 'GRAY_RELEASE', defaultValue: false, description: '是否灰度发布')
            booleanParam(name: 'ROLLBACK', defaultValue: false, description: '是否回滚')
            booleanParam(name: 'SKIP_TESTS', defaultValue: false, description: '是否跳过测试')
        }
        environment {
            TAG = "v${new Date().format('yyyyMMddHHmmss')}_${env.BUILD_NUMBER}"
        }
        stages {
            stage('初始化配置') {
                steps {
                    script {
                        // 使用新的配置管理器
                        def config = org.cicd.config.ConfigManager.getConfig(params)
                        env.CONFIG = writeJSON returnText: true, json: config

                        // 打印配置信息
                        org.cicd.config.ConfigManager.printConfig(config)
                    }
                }
            }
            stage('拉取代码') {
                steps {
                    script {
                        def config = readJSON text: env.CONFIG
                        org.cicd.utils.GitUtils.checkout(
                            config.repoUrl,
                            config.BRANCH_NAME,
                            config.credentialsId
                        )
                    }
                }
            }
            stage('配置替换') {
                steps {
                    script {
                        def config = readJSON text: env.CONFIG
                        org.cicd.config.ConfigManager.replaceConfigFiles(config)
                    }
                }
            }
            stage('构建') {
                steps {
                    script {
                        def config = readJSON text: env.CONFIG
                        def projectType = org.cicd.config.ConfigManager.detectProjectType()

                        org.cicd.utils.Logger.info("检测到项目类型: ${projectType}", "buildAndDeploy")

                        switch (projectType) {
                            case 'java':
                                org.cicd.DeployJava.build(config)
                                break
                            case 'vue':
                                org.cicd.DeployVue.build(config)
                                break
                            case 'go':
                                org.cicd.DeployGo.build(config)
                                break
                            default:
                                error "未知项目类型: ${projectType}"
                        }
                    }
                }
            }
            stage('测试') {
                when {
                    not { params.SKIP_TESTS }
                }
                steps {
                    script {
                        def config = readJSON text: env.CONFIG
                        def projectType = org.cicd.config.ConfigManager.detectProjectType()

                        switch (projectType) {
                            case 'java':
                                org.cicd.DeployJava.test(config)
                                break
                            case 'vue':
                                org.cicd.DeployVue.test(config)
                                break
                            case 'go':
                                org.cicd.DeployGo.test(config)
                                break
                        }
                    }
                }
            }
            stage('部署') {
                steps {
                    script {
                        def config = readJSON text: env.CONFIG

                        if (config.ROLLBACK) {
                            rollback(config)
                        } else if (config.GRAY_RELEASE) {
                            grayRelease(config)
                        } else {
                            switch (config.DEPLOY_TYPE) {
                                case 'docker':
                                    org.cicd.DockerUtil.buildImage(config)
                                    org.cicd.DockerUtil.runContainer(config)
                                    break
                                case 'docker-compose':
                                    org.cicd.DockerUtil.buildImage(config)
                                    org.cicd.DockerUtil.composeUp(config)
                                    break
                                case 'k8s':
                                    org.cicd.DockerUtil.buildImage(config)
                                    org.cicd.K8sUtil.apply(config)
                                    org.cicd.K8sUtil.rolloutStatus(config)
                                    break
                                default:
                                    error "未知部署类型: ${config.DEPLOY_TYPE}"
                            }
                        }
                    }
                }
            }
        }
        post {
            always {
                script {
                    def config = readJSON text: env.CONFIG
                    org.cicd.Notify.send(config)
                }
            }
            success {
                script {
                    org.cicd.utils.Logger.success("Pipeline执行成功", "buildAndDeploy")
                }
            }
            failure {
                script {
                    org.cicd.utils.Logger.error("Pipeline执行失败", "buildAndDeploy")
                    // 获取失败的Pod日志（如果是K8s部署）
                    def config = readJSON text: env.CONFIG
                    if (config.DEPLOY_TYPE == 'k8s') {
                        org.cicd.K8sUtil.getLogs(config)
                    }
                }
            }
        }
    }
}