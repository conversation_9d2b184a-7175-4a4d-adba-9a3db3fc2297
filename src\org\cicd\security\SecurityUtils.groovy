package org.cicd.security

import org.cicd.utils.Logger
import org.cicd.utils.ErrorHandler

/**
 * 安全工具类
 * 提供凭据管理、输入验证和安全检查功能
 */
class SecurityUtils implements Serializable {
    
    // 敏感信息掩码
    private static final String MASK = "****"
    
    /**
     * 安全地获取凭据
     * @param credentialsId 凭据ID
     * @param credentialsType 凭据类型 (usernamePassword, string, sshUserPrivateKey)
     */
    static def getCredentials(String credentialsId, String credentialsType = 'usernamePassword') {
        Logger.debug("获取凭据: ${credentialsId} (类型: ${credentialsType})", "SecurityUtils")
        
        try {
            switch (credentialsType) {
                case 'usernamePassword':
                    return withCredentials([usernamePassword(credentialsId: credentialsId, usernameVariable: 'USERNAME', passwordVariable: 'PASSWORD')]) {
                        return [username: env.USERNAME, password: env.PASSWORD]
                    }
                case 'string':
                    return withCredentials([string(credentialsId: credentialsId, variable: 'SECRET')]) {
                        return env.SECRET
                    }
                case 'sshUserPrivateKey':
                    return withCredentials([sshUserPrivateKey(credentialsId: credentialsId, keyFileVariable: 'SSH_KEY', usernameVariable: 'SSH_USER')]) {
                        return [username: env.SSH_USER, keyFile: env.SSH_KEY]
                    }
                default:
                    throw new IllegalArgumentException("不支持的凭据类型: ${credentialsType}")
            }
        } catch (Exception e) {
            Logger.error("获取凭据失败: ${e.message}", "SecurityUtils")
            throw new SecurityException("凭据获取失败: ${credentialsId}")
        }
    }
    
    /**
     * 验证输入参数，防止注入攻击
     * @param input 输入字符串
     * @param paramName 参数名称
     */
    static def validateInput(String input, String paramName) {
        if (!input) {
            return input
        }
        
        // 检查危险字符
        def dangerousPatterns = [
            /[;&|`$(){}[\]<>]/,  // Shell特殊字符
            /\.\./,              // 路径遍历
            /--/,                // SQL注释
            /\/\*/,              // 多行注释
            /<script/i,          // XSS
            /javascript:/i       // JavaScript协议
        ]
        
        dangerousPatterns.each { pattern ->
            if (input =~ pattern) {
                Logger.error("参数 '${paramName}' 包含危险字符: ${maskSensitiveInfo(input)}", "SecurityUtils")
                throw new SecurityException("参数 '${paramName}' 包含不安全的字符")
            }
        }
        
        return input
    }
    
    /**
     * 验证镜像名称安全性
     * @param imageName 镜像名称
     */
    static def validateImageNameSecurity(String imageName) {
        validateInput(imageName, "imageName")
        
        // 检查是否包含私有仓库地址
        if (imageName.contains('localhost') || imageName.contains('127.0.0.1')) {
            Logger.warning("镜像名称包含本地地址，请确认安全性", "SecurityUtils")
        }
        
        // 检查是否使用latest标签
        if (imageName.endsWith(':latest')) {
            Logger.warning("使用latest标签可能导致版本不一致，建议使用具体版本", "SecurityUtils")
        }
    }
    
    /**
     * 验证Git URL安全性
     * @param gitUrl Git仓库地址
     */
    static def validateGitUrlSecurity(String gitUrl) {
        validateInput(gitUrl, "gitUrl")
        
        // 检查协议安全性
        if (gitUrl.startsWith('http://')) {
            Logger.warning("Git URL使用HTTP协议，建议使用HTTPS或SSH", "SecurityUtils")
        }
        
        // 检查是否包含凭据信息
        if (gitUrl.contains('@') && gitUrl.contains('://')) {
            def urlParts = gitUrl.split('://')
            if (urlParts.length > 1 && urlParts[1].contains('@')) {
                Logger.error("Git URL包含凭据信息，存在安全风险", "SecurityUtils")
                throw new SecurityException("Git URL不应包含明文凭据")
            }
        }
    }
    
    /**
     * 安全地执行Shell命令
     * @param command 命令
     * @param allowedCommands 允许的命令列表
     */
    static def secureShellExecution(String command, List<String> allowedCommands = []) {
        // 验证命令安全性
        validateInput(command, "command")
        
        // 检查是否在允许列表中
        if (allowedCommands && !allowedCommands.any { command.startsWith(it) }) {
            Logger.error("命令不在允许列表中: ${maskSensitiveInfo(command)}", "SecurityUtils")
            throw new SecurityException("不允许执行的命令")
        }
        
        // 检查危险命令
        def dangerousCommands = ['rm -rf', 'dd if=', 'mkfs', 'fdisk', 'format', 'del /f', 'rmdir /s']
        dangerousCommands.each { dangerous ->
            if (command.toLowerCase().contains(dangerous)) {
                Logger.error("检测到危险命令: ${dangerous}", "SecurityUtils")
                throw new SecurityException("禁止执行危险命令")
            }
        }
        
        Logger.debug("执行安全验证通过的命令", "SecurityUtils")
        return ErrorHandler.executeShell(command, "安全Shell执行")
    }
    
    /**
     * 掩码敏感信息
     * @param text 原始文本
     */
    static def maskSensitiveInfo(String text) {
        if (!text) {
            return text
        }
        
        // 掩码密码、token等敏感信息
        def patterns = [
            [pattern: /password[=:]\s*[^\s&]+/i, replacement: 'password=' + MASK],
            [pattern: /token[=:]\s*[^\s&]+/i, replacement: 'token=' + MASK],
            [pattern: /key[=:]\s*[^\s&]+/i, replacement: 'key=' + MASK],
            [pattern: /secret[=:]\s*[^\s&]+/i, replacement: 'secret=' + MASK],
            [pattern: /api_key[=:]\s*[^\s&]+/i, replacement: 'api_key=' + MASK]
        ]
        
        def maskedText = text
        patterns.each { p ->
            maskedText = maskedText.replaceAll(p.pattern, p.replacement)
        }
        
        return maskedText
    }
    
    /**
     * 检查文件权限
     * @param filePath 文件路径
     */
    static def checkFilePermissions(String filePath) {
        try {
            def permissions = ErrorHandler.executeShell("ls -la ${filePath} | awk '{print \$1}'", "检查文件权限", true).trim()
            
            // 检查是否所有人可写
            if (permissions.endsWith('w')) {
                Logger.warning("文件 ${filePath} 对所有人可写，存在安全风险", "SecurityUtils")
            }
            
            // 检查是否所有人可执行
            if (permissions.endsWith('x')) {
                Logger.warning("文件 ${filePath} 对所有人可执行，存在安全风险", "SecurityUtils")
            }
            
            Logger.debug("文件权限检查完成: ${filePath} (${permissions})", "SecurityUtils")
            
        } catch (Exception e) {
            Logger.warning("无法检查文件权限: ${e.message}", "SecurityUtils")
        }
    }
    
    /**
     * 生成安全的临时文件名
     */
    static def generateSecureTempFileName(String prefix = "temp") {
        def timestamp = System.currentTimeMillis()
        def random = new Random().nextInt(10000)
        return "${prefix}_${timestamp}_${random}"
    }
    
    /**
     * 清理临时文件
     * @param tempFiles 临时文件列表
     */
    static def cleanupTempFiles(List<String> tempFiles) {
        tempFiles.each { file ->
            try {
                if (fileExists(file)) {
                    ErrorHandler.executeShell("rm -f ${file}", "清理临时文件")
                    Logger.debug("清理临时文件: ${file}", "SecurityUtils")
                }
            } catch (Exception e) {
                Logger.warning("清理临时文件失败: ${file} - ${e.message}", "SecurityUtils")
            }
        }
    }
}
