package org.cicd

import org.cicd.utils.Logger

/**
 * Vue项目构建和部署工具类
 */
class DeployVue implements Serializable {

    /**
     * 构建Vue项目
     */
    static def build(Map params) {
        Logger.stageStart("Vue项目构建")

        try {
            // 检查包管理器
            def packageManager = getPackageManager()
            Logger.info("使用包管理器: ${packageManager}", "DeployVue")

            // 安装依赖
            Logger.info("安装依赖", "DeployVue")
            sh "${packageManager} install"

            // 构建项目
            Logger.info("构建项目", "DeployVue")
            sh "${packageManager} run build"

            Logger.success("Vue项目构建完成", "DeployVue")
        } catch (Exception e) {
            Logger.error("Vue项目构建失败: ${e.message}", "DeployVue")
            throw e
        } finally {
            Logger.stageEnd("Vue项目构建")
        }
    }

    /**
     * 运行Vue项目测试
     */
    static def test(Map params) {
        Logger.stageStart("Vue项目测试")

        try {
            def packageManager = getPackageManager()
            Logger.info("运行测试", "DeployVue")

            // 检查是否有测试脚本
            def packageJson = readJSON file: 'package.json'
            if (packageJson.scripts && packageJson.scripts.test) {
                sh "${packageManager} run test"
                Logger.success("Vue项目测试完成", "DeployVue")
            } else {
                Logger.warning("未找到测试脚本，跳过测试", "DeployVue")
            }
        } catch (Exception e) {
            Logger.warning("Vue项目测试失败，但继续执行: ${e.message}", "DeployVue")
        } finally {
            Logger.stageEnd("Vue项目测试")
        }
    }

    /**
     * 部署Vue项目
     */
    static def deploy(Map params) {
        Logger.info("Vue项目部署逻辑可在此扩展", "DeployVue")
        // TODO: 实现具体的部署逻辑
    }

    /**
     * 检测包管理器
     */
    private static def getPackageManager() {
        if (fileExists('yarn.lock')) {
            return 'yarn'
        } else if (fileExists('pnpm-lock.yaml')) {
            return 'pnpm'
        } else {
            return 'npm'
        }
    }
}