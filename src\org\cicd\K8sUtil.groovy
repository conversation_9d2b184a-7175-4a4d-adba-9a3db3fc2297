package org.cicd

import org.cicd.utils.Logger
import org.cicd.utils.ErrorHandler
import org.cicd.utils.ConfigValidator

/**
 * Kubernetes操作工具类
 * 提供K8s部署、回滚等功能
 */
class K8sUtil implements Serializable {

    /**
     * 应用K8s配置文件
     */
    static def apply(Map params) {
        Logger.stageStart("Kubernetes部署")

        try {
            def namespace = params.k8sNamespace ?: 'default'
            ConfigValidator.validateNamespace(namespace)

            // 检查k8s目录是否存在
            ErrorHandler.requireFileExists('k8s', 'k8s配置目录不存在')

            Logger.info("应用Kubernetes配置到命名空间: ${namespace}", "K8sUtil")

            // 创建命名空间（如果不存在）
            createNamespaceIfNotExists(namespace)

            // 应用配置
            ErrorHandler.executeWithErrorHandling({
                ErrorHandler.executeShell("kubectl apply -f k8s/ -n ${namespace}", "K8s配置应用")
            }, "K8s配置应用", 2, 10)

            Logger.success("Kubernetes配置应用成功", "K8sUtil")

        } catch (Exception e) {
            Logger.error("Kubernetes部署失败: ${e.message}", "K8sUtil")
            throw e
        } finally {
            Logger.stageEnd("Kubernetes部署")
        }
    }

    /**
     * 检查部署状态
     */
    static def rolloutStatus(Map params) {
        Logger.stageStart("检查部署状态")

        try {
            def namespace = params.k8sNamespace ?: 'default'
            def deploymentName = params.appName ?: getFirstDeploymentName(namespace)

            Logger.info("检查部署状态: ${deploymentName} (命名空间: ${namespace})", "K8sUtil")

            ErrorHandler.executeWithErrorHandling({
                ErrorHandler.executeShell("kubectl rollout status deployment/${deploymentName} -n ${namespace} --timeout=300s", "检查部署状态")
            }, "检查部署状态", 1, 30)

            Logger.success("部署状态检查完成", "K8sUtil")

        } catch (Exception e) {
            Logger.error("部署状态检查失败: ${e.message}", "K8sUtil")
            throw e
        } finally {
            Logger.stageEnd("检查部署状态")
        }
    }

    /**
     * 回滚部署
     */
    static def rollback(Map params) {
        Logger.stageStart("Kubernetes回滚")

        try {
            def namespace = params.k8sNamespace ?: 'default'
            def deploymentName = params.appName ?: getFirstDeploymentName(namespace)

            Logger.info("回滚部署: ${deploymentName} (命名空间: ${namespace})", "K8sUtil")

            ErrorHandler.executeWithErrorHandling({
                ErrorHandler.executeShell("kubectl rollout undo deployment/${deploymentName} -n ${namespace}", "K8s回滚")
            }, "K8s回滚", 2, 10)

            // 等待回滚完成
            Logger.info("等待回滚完成", "K8sUtil")
            ErrorHandler.executeShell("kubectl rollout status deployment/${deploymentName} -n ${namespace} --timeout=300s", "等待回滚完成")

            Logger.success("Kubernetes回滚完成", "K8sUtil")

        } catch (Exception e) {
            Logger.error("Kubernetes回滚失败: ${e.message}", "K8sUtil")
            throw e
        } finally {
            Logger.stageEnd("Kubernetes回滚")
        }
    }

    /**
     * 获取Pod日志
     */
    static def getLogs(Map params) {
        try {
            def namespace = params.k8sNamespace ?: 'default'
            def appName = params.appName

            if (!appName) {
                Logger.warning("未指定应用名称，跳过日志获取", "K8sUtil")
                return
            }

            Logger.info("获取Pod日志: ${appName}", "K8sUtil")

            def logs = ErrorHandler.executeShell("kubectl logs -l app=${appName} -n ${namespace} --tail=50", "获取Pod日志", true)
            Logger.info("Pod日志:\n${logs}", "K8sUtil")

        } catch (Exception e) {
            Logger.warning("获取Pod日志失败: ${e.message}", "K8sUtil")
        }
    }

    /**
     * 创建命名空间（如果不存在）
     */
    private static def createNamespaceIfNotExists(String namespace) {
        if (namespace == 'default') {
            return // default命名空间总是存在
        }

        ErrorHandler.safeExecute({
            def exists = ErrorHandler.executeShell("kubectl get namespace ${namespace}", "检查命名空间", true)
            if (!exists) {
                ErrorHandler.executeShell("kubectl create namespace ${namespace}", "创建命名空间")
                Logger.info("创建命名空间: ${namespace}", "K8sUtil")
            }
        }, "创建命名空间 ${namespace}")
    }

    /**
     * 获取第一个部署的名称
     */
    private static def getFirstDeploymentName(String namespace) {
        try {
            return ErrorHandler.executeShell("kubectl get deploy -o jsonpath='{.items[0].metadata.name}' -n ${namespace}", "获取部署名称", true).trim()
        } catch (Exception e) {
            Logger.warning("无法获取部署名称，使用默认值", "K8sUtil")
            return "app"
        }
    }
}