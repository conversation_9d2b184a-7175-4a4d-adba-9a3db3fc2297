# Jenkins 构建节点选择指南

## 📋 概述

Jenkins共享库现在支持动态选择构建节点，允许用户根据项目需求和资源情况选择最适合的构建环境。

## 🎯 支持的节点类型

### 默认节点选项

| 节点标签 | 描述 | 适用场景 |
|---------|------|----------|
| `any` | 任意可用节点 | 通用构建，无特殊要求 |
| `master` | Jenkins主节点 | 轻量级任务，配置管理 |
| `docker-node` | 支持Docker的节点 | Docker镜像构建和容器部署 |
| `k8s-node` | 支持Kubernetes的节点 | K8s应用部署和管理 |
| `linux-node` | Linux系统节点 | Linux环境下的构建和部署 |
| `windows-node` | Windows系统节点 | .NET应用或Windows特定任务 |

## 🔧 配置说明

### 1. Jenkins节点标签配置

在Jenkins中为每个节点配置相应的标签：

#### 配置步骤：
1. 进入 Jenkins 管理 → 节点管理
2. 选择要配置的节点
3. 点击"配置"
4. 在"标签"字段中添加相应标签

#### 示例配置：
```bash
# Docker构建节点
标签: docker-node linux docker-enabled

# K8s部署节点  
标签: k8s-node linux kubectl-enabled

# Windows构建节点
标签: windows-node windows dotnet-enabled
```

### 2. 自定义节点选项

如果需要添加自定义节点选项，修改 `Jenkinsfile` 中的参数定义：

```groovy
choice(
    name: 'BUILD_NODE',
    choices: [
        'any', 
        'master', 
        'docker-node', 
        'k8s-node', 
        'linux-node', 
        'windows-node',
        'gpu-node',        // 添加GPU节点
        'high-memory-node' // 添加高内存节点
    ],
    description: '选择构建节点'
)
```

## 🚀 使用方法

### 1. 通过Jenkins UI选择

1. 打开Jenkins项目页面
2. 点击"Build with Parameters"
3. 在"BUILD_NODE"下拉菜单中选择合适的节点
4. 配置其他参数后点击"构建"

### 2. 通过API调用

```bash
curl -X POST "http://jenkins-url/job/project-name/buildWithParameters" \
  --data "BUILD_NODE=docker-node" \
  --data "DEPLOY_ENV=prod" \
  --data "DEPLOY_BRANCH=main"
```

### 3. 通过Pipeline脚本

```groovy
build job: 'project-name', parameters: [
    choice(name: 'BUILD_NODE', value: 'k8s-node'),
    choice(name: 'DEPLOY_ENV', value: 'prod'),
    string(name: 'DEPLOY_BRANCH', value: 'main')
]
```

## 📊 节点选择策略

### 根据项目类型选择

| 项目类型 | 推荐节点 | 原因 |
|---------|----------|------|
| Java应用 | `linux-node` | 稳定的Linux环境，支持Maven/Gradle |
| Vue/React应用 | `linux-node` | Node.js环境，npm/yarn支持 |
| Go应用 | `linux-node` | 原生Linux支持，交叉编译 |
| .NET应用 | `windows-node` | Windows环境，Visual Studio支持 |
| Docker应用 | `docker-node` | Docker守护进程，镜像构建 |
| K8s应用 | `k8s-node` | kubectl工具，集群访问权限 |

### 根据部署环境选择

| 部署环境 | 推荐节点 | 说明 |
|---------|----------|------|
| `dev` | `any` | 开发环境，资源要求不高 |
| `test` | `linux-node` | 测试环境，稳定的Linux环境 |
| `prod` | `k8s-node` | 生产环境，专用K8s部署节点 |
| `gray` | `k8s-node` | 灰度环境，需要K8s支持 |

## 🔍 监控和调试

### 1. 查看节点信息

在构建日志中会显示详细的节点信息：

```
=== 构建环境信息 ===
构建节点: docker-node-01
工作空间: /var/jenkins_home/workspace/project-name
构建号: 123
=====================
```

### 2. 节点健康检查

```groovy
// 在Jenkinsfile中添加节点检查
stage('节点检查') {
    steps {
        script {
            echo "当前节点: ${env.NODE_NAME}"
            echo "节点标签: ${env.NODE_LABELS}"
            
            // 检查Docker可用性
            if (params.BUILD_NODE == 'docker-node') {
                sh 'docker --version'
                sh 'docker info'
            }
            
            // 检查kubectl可用性
            if (params.BUILD_NODE == 'k8s-node') {
                sh 'kubectl version --client'
                sh 'kubectl cluster-info'
            }
        }
    }
}
```

## ⚠️ 注意事项

### 1. 节点可用性

- 确保选择的节点在线且可用
- 建议配置多个相同类型的节点以提供冗余
- 定期检查节点健康状态

### 2. 资源限制

- 不同节点可能有不同的资源限制
- 根据项目需求选择合适配置的节点
- 避免在资源不足的节点上运行大型构建

### 3. 权限配置

- 确保节点有访问Git仓库的权限
- 配置Docker Registry访问权限
- 设置K8s集群访问凭据

## 🛠️ 故障排查

### 常见问题

1. **节点不可用**
   ```
   解决方案：
   - 检查节点是否在线
   - 验证节点标签配置
   - 选择其他可用节点
   ```

2. **权限不足**
   ```
   解决方案：
   - 检查节点用户权限
   - 配置必要的凭据
   - 验证工具安装情况
   ```

3. **资源不足**
   ```
   解决方案：
   - 选择配置更高的节点
   - 清理节点磁盘空间
   - 调整构建参数
   ```

## 📈 最佳实践

1. **标签规范化**：使用统一的标签命名规范
2. **节点分类**：按功能和环境对节点进行分类
3. **负载均衡**：配置多个同类型节点分散负载
4. **监控告警**：设置节点状态监控和告警
5. **定期维护**：定期清理和更新节点环境

---

📝 **文档版本**: v1.0  
🕐 **更新时间**: 2024-12-10  
👥 **维护团队**: DevOps Team
