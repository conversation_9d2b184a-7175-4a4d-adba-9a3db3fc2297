<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="ALL" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="13163e8a-663d-402c-85ef-f7615bdad9e3" name="Changes" comment="fix: kcp port">
      <change beforePath="$PROJECT_DIR$/resources/app-backend-gateway/k8s/k8s-deployment.yml.vm" beforeDir="false" afterPath="$PROJECT_DIR$/resources/app-backend-gateway/k8s/k8s-deployment.yml.vm" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="GOROOT" url="file://C:/Program Files/Go" />
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="2q8qPc0MnPSF2Z19m5Sikrqvh1k" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.go.formatter.settings.were.checked": "true",
    "RunOnceActivity.go.migrated.go.modules.settings": "true",
    "RunOnceActivity.go.modules.go.list.on.any.changes.was.set": "true",
    "RunOnceActivity.go.watchers.conflict.with.on.save.actions.check.performed": "true",
    "WebServerToolWindowFactoryState": "false",
    "git-widget-placeholder": "old",
    "go.import.settings.migrated": "true",
    "go.sdk.automatically.set": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/Projects/Note2024/dev/mango-game/mango-admin",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager">
    <configuration default="true" type="JetRunConfigurationType">
      <module name="jenkinslib" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="jenkinslib" />
      <option name="filePath" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="PythonConfigurationType" factoryName="Python">
      <module name="jenkinslib" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="Python.FlaskServer">
      <module name="jenkinslib" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="launchJavascriptDebuger" value="false" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="Tox" factoryName="Tox">
      <module name="jenkinslib" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="docs" factoryName="Docutils task">
      <module name="jenkinslib" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="docutils_input_file" value="" />
      <option name="docutils_output_file" value="" />
      <option name="docutils_params" value="" />
      <option name="docutils_task" value="" />
      <option name="docutils_open_in_browser" value="false" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="docs" factoryName="Sphinx task">
      <module name="jenkinslib" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="docutils_input_file" value="" />
      <option name="docutils_output_file" value="" />
      <option name="docutils_params" value="" />
      <option name="docutils_task" value="" />
      <option name="docutils_open_in_browser" value="false" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="GoApplicationRunConfiguration" factoryName="Go Application">
      <module name="jenkinslib" />
      <working_directory value="$PROJECT_DIR$" />
      <go_parameters value="-i" />
      <kind value="FILE" />
      <directory value="$PROJECT_DIR$" />
      <filePath value="$PROJECT_DIR$" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="GoTestRunConfiguration" factoryName="Go Test">
      <module name="jenkinslib" />
      <working_directory value="$PROJECT_DIR$" />
      <go_parameters value="-i" />
      <kind value="DIRECTORY" />
      <directory value="$PROJECT_DIR$" />
      <filePath value="$PROJECT_DIR$" />
      <framework value="gotest" />
      <method v="2" />
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-b114ca120d71-intellij.indexing.shared.core-IU-242.20224.419" />
        <option value="bundled-js-predefined-d6986cc7102b-410509235cf1-JavaScript-IU-242.20224.419" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="13163e8a-663d-402c-85ef-f7615bdad9e3" name="Changes" comment="" />
      <created>1734054537265</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1734054537265</updated>
      <workItem from="1734054542662" duration="4616000" />
      <workItem from="1734399200562" duration="1565000" />
      <workItem from="1734490039939" duration="1903000" />
      <workItem from="1734493581372" duration="158000" />
      <workItem from="1734493757896" duration="149000" />
      <workItem from="1735023223253" duration="3753000" />
      <workItem from="1735212407365" duration="14092000" />
      <workItem from="1735555193548" duration="10306000" />
      <workItem from="1735868006722" duration="1234000" />
      <workItem from="1735884297966" duration="11588000" />
      <workItem from="1736308419820" duration="524000" />
    </task>
    <task id="LOCAL-00001" summary="fix: logs">
      <option name="closed" value="true" />
      <created>1734490184144</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1734490184144</updated>
    </task>
    <task id="LOCAL-00002" summary="            - name: http-9006&#10;              containerPort: 9006&#10;              protocol: TCP&#10;            - name: kcp-30023&#10;              containerPort: 30023&#10;              protocol: UDP">
      <option name="closed" value="true" />
      <created>1734490441310</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1734490441310</updated>
    </task>
    <task id="LOCAL-00003" summary="pod间沁源县配置">
      <option name="closed" value="true" />
      <created>1735032233909</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1735032233909</updated>
    </task>
    <task id="LOCAL-00004" summary="fix: kcp port">
      <option name="closed" value="true" />
      <created>1735032701395</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1735032701395</updated>
    </task>
    <task id="LOCAL-00005" summary="fix: kcp port">
      <option name="closed" value="true" />
      <created>1735212520471</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1735212520471</updated>
    </task>
    <task id="LOCAL-00006" summary="fix: kcp port">
      <option name="closed" value="true" />
      <created>1735264863230</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1735264863230</updated>
    </task>
    <task id="LOCAL-00007" summary="fix: kcp port">
      <option name="closed" value="true" />
      <created>1735525188084</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1735525188084</updated>
    </task>
    <task id="LOCAL-00008" summary="fix: kcp port">
      <option name="closed" value="true" />
      <created>1735528089248</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1735528089248</updated>
    </task>
    <task id="LOCAL-00009" summary="fix: kcp port">
      <option name="closed" value="true" />
      <created>1735528305166</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1735528305166</updated>
    </task>
    <task id="LOCAL-00010" summary="fix: kcp port">
      <option name="closed" value="true" />
      <created>1735611091747</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1735611091747</updated>
    </task>
    <task id="LOCAL-00011" summary="fix: kcp port">
      <option name="closed" value="true" />
      <created>1735613872271</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1735613872271</updated>
    </task>
    <task id="LOCAL-00012" summary="fix: kcp port">
      <option name="closed" value="true" />
      <created>1735614052565</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1735614052565</updated>
    </task>
    <task id="LOCAL-00013" summary="fix: kcp port">
      <option name="closed" value="true" />
      <created>1735614367657</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1735614367657</updated>
    </task>
    <task id="LOCAL-00014" summary="fix: kcp port">
      <option name="closed" value="true" />
      <created>1735616976301</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1735616976301</updated>
    </task>
    <task id="LOCAL-00015" summary="fix: kcp port">
      <option name="closed" value="true" />
      <created>1735619167989</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1735619167990</updated>
    </task>
    <task id="LOCAL-00016" summary="fix: kcp port">
      <option name="closed" value="true" />
      <created>1735627930317</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1735627930317</updated>
    </task>
    <task id="LOCAL-00017" summary="fix: kcp port">
      <option name="closed" value="true" />
      <created>1735915654163</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1735915654163</updated>
    </task>
    <task id="LOCAL-00018" summary="fix: kcp port">
      <option name="closed" value="true" />
      <created>1735917064319</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1735917064319</updated>
    </task>
    <task id="LOCAL-00019" summary="fix: kcp port">
      <option name="closed" value="true" />
      <created>1735917808443</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1735917808443</updated>
    </task>
    <task id="LOCAL-00020" summary="fix: kcp port">
      <option name="closed" value="true" />
      <created>1735918218561</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1735918218562</updated>
    </task>
    <task id="LOCAL-00021" summary="fix: kcp port">
      <option name="closed" value="true" />
      <created>1736148412508</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1736148412508</updated>
    </task>
    <task id="LOCAL-00022" summary="fix: kcp port">
      <option name="closed" value="true" />
      <created>1736173102474</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1736173102474</updated>
    </task>
    <option name="localTasksCounter" value="23" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="fix: logs" />
    <MESSAGE value="            - name: http-9006&#10;              containerPort: 9006&#10;              protocol: TCP&#10;            - name: kcp-30023&#10;              containerPort: 30023&#10;              protocol: UDP" />
    <MESSAGE value="pod间沁源县配置" />
    <MESSAGE value="fix: kcp port" />
    <option name="LAST_COMMIT_MESSAGE" value="fix: kcp port" />
  </component>
  <component name="VgoProject">
    <settings-migrated>true</settings-migrated>
  </component>
</project>