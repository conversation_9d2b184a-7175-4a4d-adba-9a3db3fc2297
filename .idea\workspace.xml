<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="ALL" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="37616c9f-28cf-41a9-abf6-378d70091c6e" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/.idea/.gitignore" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/misc.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/modules.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/modules.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/vcs.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/vcs.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Jenkinsfile" beforeDir="false" afterPath="$PROJECT_DIR$/Jenkinsfile" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/README.en.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/README.md" beforeDir="false" afterPath="$PROJECT_DIR$/README.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jenkinslib.iml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/admin-client/docker/default.conf.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/admin-client/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/admin-client/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/admin-client/docker/juat.cube.ganrobot.com.crt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/admin-client/docker/juat.cube.ganrobot.com.key" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/admin-client/k8s/k8s-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/admin-client/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/admin-client/k8s/k8s-hpa.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/admin-client/k8s/k8s-ingress.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/admin-client/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/admin-server/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/admin-server/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/admin-server/k8s/k8s-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/admin-server/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/admin-server/k8s/k8s-hpa.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/admin-server/k8s/k8s-ingress.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/admin-server/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/admin-server/k8s/k8s-timezone-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/app-backend-gateway/app-backend-gateway.iml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/app-backend-gateway/docker/boot.sh.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/app-backend-gateway/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/app-backend-gateway/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/app-backend-gateway/docker/pipeline.groovy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/app-backend-gateway/k8s/k8s-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/app-backend-gateway/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/app-backend-gateway/k8s/k8s-hpa.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/app-backend-gateway/k8s/k8s-ingress.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/app-backend-gateway/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/app-backend-gateway/k8s/k8s-timezone-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/bluetooth-debug/docker/default.conf.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/bluetooth-debug/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/bluetooth-debug/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/bluetooth-debug/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/bluetooth-debug/k8s/k8s-ingress.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/bluetooth-debug/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/canal/canal_admin/application.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/canal/canal_deploy/application.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/canal/canal_deploy/syncall.sh" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/canal/canal_deploy/template.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/canal/canal_deployer/canal.properties" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/canal/canal_deployer/canal_local.properties" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/canal/canal_deployer/example/instance.propertios" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/competition-hall/docker/default.conf.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/competition-hall/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/competition-hall/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/competition-hall/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/competition-hall/k8s/k8s-ingress.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/competition-hall/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/competitive-manage/config/logging.toml.dev" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/competitive-manage/config/logging.toml.ggprod" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/competitive-manage/config/logging.toml.gguat" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/competitive-manage/config/logging.toml.prod" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/competitive-manage/config/logging.toml.test" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/competitive-manage/config/logging.toml.uat" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/competitive-manage/config/logging.toml.verify" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/competitive-manage/config/middleware.toml.dev" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/competitive-manage/config/middleware.toml.ggprod" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/competitive-manage/config/middleware.toml.gguat" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/competitive-manage/config/middleware.toml.prod" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/competitive-manage/config/middleware.toml.test" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/competitive-manage/config/middleware.toml.uat" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/competitive-manage/config/middleware.toml.verify" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/competitive-manage/config/server.toml.dev" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/competitive-manage/config/server.toml.ggprod" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/competitive-manage/config/server.toml.gguat" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/competitive-manage/config/server.toml.prod" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/competitive-manage/config/server.toml.test" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/competitive-manage/config/server.toml.uat" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/competitive-manage/config/server.toml.verify" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/competitive-manage/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/competitive-manage/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/competitive-manage/docker/dockerfile.vm.bak" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/competitive-manage/k8s/k8s-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/competitive-manage/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/competitive-manage/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-academy-service/cube-academy-service.iml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-academy-service/docker/boot.sh.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-academy-service/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-academy-service/docker/dockerfile copy.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-academy-service/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-academy-service/docker/dockerfileopenjdk17.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-academy-service/docker/dockerfileopenjdk21.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-academy-service/docker/pipeline.groovy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-academy-service/k8s/k8s-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-academy-service/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-academy-service/k8s/k8s-hpa.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-academy-service/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-academy-service/k8s/k8s-timezone-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-appstore/docker/default.conf.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-appstore/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-appstore/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-appstore/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-appstore/k8s/k8s-ingress.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-appstore/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-baike-server/config/wp-config.php.prod" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-baike-server/config/wp-config.php.test" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-baike-server/cube-baike-server.iml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-baike-server/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-baike-server/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-baike-server/docker/http.conf.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-baike-server/docker/pipeline.groovy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-baike-server/k8s/k8s-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-baike-server/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-baike-server/k8s/k8s-hpa.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-baike-server/k8s/k8s-ingress.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-baike-server/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-battle-service/cube-battle-service.iml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-battle-service/docker/boot.sh.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-battle-service/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-battle-service/docker/dockerfile copy.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-battle-service/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-battle-service/docker/dockerfileopenjdk17.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-battle-service/docker/dockerfileopenjdk21.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-battle-service/docker/pipeline.groovy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-battle-service/k8s/k8s-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-battle-service/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-battle-service/k8s/k8s-hpa.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-battle-service/k8s/k8s-ingress.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-battle-service/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-battle-service/k8s/k8s-timezone-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-common-service/cube-common-service.iml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-common-service/docker/boot.sh.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-common-service/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-common-service/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-common-service/docker/pipeline.groovy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-common-service/k8s/k8s-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-common-service/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-common-service/k8s/k8s-hpa.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-common-service/k8s/k8s-ingress.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-common-service/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-common-service/k8s/k8s-timezone-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-formula-library/cicd.groovy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-formula-library/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-formula-library/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-formula-library/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-formula-library/k8s/k8s-ingress.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-formula-library/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-formula-library/test.groovy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-im-service/Jenkinsfile.groovy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-im-service/docker/boot.sh.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-im-service/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-im-service/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-im-service/docker/pipeline.groovy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-im-service/k8s/k8s-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-im-service/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-im-service/k8s/k8s-deployment.yml2.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-im-service/k8s/k8s-hpa.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-im-service/k8s/k8s-ingress.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-im-service/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-im-service/k8s/k8s-tz-losangeles.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-im-service/k8s/k8s-tz-shanghai.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-ketang-admin/cube-ketang-admin.iml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-ketang-admin/docker/default.conf.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-ketang-admin/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-ketang-admin/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-ketang-admin/docker/pipeline.groovy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-ketang-admin/k8s/k8s-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-ketang-admin/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-ketang-admin/k8s/k8s-hpa.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-ketang-admin/k8s/k8s-ingress.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-ketang-admin/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-ketang-h5/cube-ketang-h5.iml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-ketang-h5/docker/default.conf.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-ketang-h5/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-ketang-h5/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-ketang-h5/docker/pipeline.groovy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-ketang-h5/k8s/k8s-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-ketang-h5/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-ketang-h5/k8s/k8s-hpa.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-ketang-h5/k8s/k8s-ingress.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-ketang-h5/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-ketang-server/config/.env.prod" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-ketang-server/config/.env.test" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-ketang-server/cube-ketang-server.iml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-ketang-server/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-ketang-server/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-ketang-server/docker/http.conf.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-ketang-server/docker/pipeline.groovy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-ketang-server/k8s/k8s-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-ketang-server/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-ketang-server/k8s/k8s-hpa.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-ketang-server/k8s/k8s-ingress.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-ketang-server/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-lost-test/docker/default.conf.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-lost-test/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-lost-test/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-lost-test/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-lost-test/k8s/k8s-ingress.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-lost-test/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-push-service/cube-push-service.iml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-push-service/docker/boot.sh.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-push-service/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-push-service/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-push-service/docker/pipeline.groovy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-push-service/k8s/k8s-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-push-service/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-push-service/k8s/k8s-deployment.yml.vm.bak" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-push-service/k8s/k8s-hpa.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-push-service/k8s/k8s-ingress.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-push-service/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-push-service/k8s/k8s-timezone-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-race-service/cube-race-service.iml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-race-service/docker/boot.sh.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-race-service/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-race-service/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-race-service/docker/pipeline.groovy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-race-service/k8s/k8s-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-race-service/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-race-service/k8s/k8s-hpa.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-race-service/k8s/k8s-ingress.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-race-service/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-race-service/k8s/k8s-timezone-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-racing-car/docker/default.conf.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-racing-car/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-racing-car/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-racing-car/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-racing-car/k8s/k8s-ingress.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-racing-car/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-rank-service/cube-rank-service.iml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-rank-service/docker/boot.sh.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-rank-service/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-rank-service/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-rank-service/docker/pipeline.groovy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-rank-service/k8s/k8s-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-rank-service/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-rank-service/k8s/k8s-hpa.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-rank-service/k8s/k8s-ingress.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-rank-service/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-rank-service/k8s/k8s-tz-losangeles.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-rank-service/k8s/k8s-tz-shanghai.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-review-service/cube-review-service.iml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-review-service/docker/boot.sh.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-review-service/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-review-service/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-review-service/docker/pipeline.groovy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-review-service/k8s/k8s-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-review-service/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-review-service/k8s/k8s-deployment.yml2.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-review-service/k8s/k8s-hpa.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-review-service/k8s/k8s-ingress.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-review-service/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-review-service/k8s/k8s-tz-losangeles.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-review-service/k8s/k8s-tz-shanghai.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-time-trial-service/cube-time-trial-service.iml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-time-trial-service/docker/boot.sh.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-time-trial-service/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-time-trial-service/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-time-trial-service/docker/pipeline.groovy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-time-trial-service/k8s/k8s-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-time-trial-service/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-time-trial-service/k8s/k8s-hpa.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-time-trial-service/k8s/k8s-ingress.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-time-trial-service/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-time-trial-service/k8s/k8s-tz-losangeles.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-time-trial-service/k8s/k8s-tz-shanghai.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-toutiao-server/config/wp-config.php.prod" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-toutiao-server/config/wp-config.php.test" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-toutiao-server/cube-toutiao-server.iml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-toutiao-server/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-toutiao-server/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-toutiao-server/docker/http.conf.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-toutiao-server/docker/pipeline.groovy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-toutiao-server/k8s/k8s-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-toutiao-server/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-toutiao-server/k8s/k8s-hpa.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-toutiao-server/k8s/k8s-ingress.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-toutiao-server/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-video-server/config/wp-config.php.prod" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-video-server/config/wp-config.php.test" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-video-server/cube-video-server.iml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-video-server/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-video-server/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-video-server/docker/http.conf.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-video-server/docker/pipeline.groovy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-video-server/k8s/k8s-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-video-server/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-video-server/k8s/k8s-hpa.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-video-server/k8s/k8s-ingress.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cube-video-server/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cubestation-portal/docker/default.conf.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cubestation-portal/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cubestation-portal/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cubestation-portal/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cubestation-portal/k8s/k8s-ingress.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/cubestation-portal/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/data-center-service/data-center-service.iml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/data-center-service/docker/boot.sh.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/data-center-service/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/data-center-service/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/data-center-service/docker/pipeline.groovy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/data-center-service/k8s/k8s-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/data-center-service/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/data-center-service/k8s/k8s-hpa.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/data-center-service/k8s/k8s-ingress.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/data-center-service/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/data-center-service/k8s/k8s-timezone-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/datax/all.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/datax/bat_incrt.sh" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/datax/batch.sh" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/datax/gen_conf.sh" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/datax/increment.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/datax/increment_sync.sh" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/datax/tables01.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/factory-simple-test/docker/default.conf.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/factory-simple-test/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/factory-simple-test/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/factory-simple-test/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/factory-simple-test/k8s/k8s-ingress.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/factory-simple-test/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/gancloud-backend/docker/copy.sh.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/gancloud-backend/docker/docker-build.sh.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/gancloud-backend/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/gancloud-backend/k8s/k8s-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/gancloud-backend/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/gancloud-backend/k8s/k8s-hpa.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/gancloud-backend/k8s/k8s-ingress.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/gancloud-backend/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/gancloud-backend/k8s/k8s-timezone-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/gancloud-frontend/docker/default.conf.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/gancloud-frontend/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/gancloud-frontend/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/gancloud-frontend/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/gancloud-frontend/k8s/k8s-ingress.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/gancloud-frontend/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/go-socket-server/config/config.yaml.dev" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/go-socket-server/config/config.yaml.test" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/go-socket-server/config/config.yaml.uat" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/go-socket-server/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/go-socket-server/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/go-socket-server/docker/pipeline.groovy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/go-socket-server/go-socket-server.iml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/go-socket-server/k8s/k8s-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/go-socket-server/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/go-zero-micro-app/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/go-zero-micro-app/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/go-zero-micro-app/k8s/k8s-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/go-zero-micro-app/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/go-zero-micro-app/k8s/k8s-hpa.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/go-zero-micro-app/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/go-zero-micro-library/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/go-zero-micro-library/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/go-zero-micro-library/k8s/k8s-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/go-zero-micro-library/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/go-zero-micro-library/k8s/k8s-hpa.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/go-zero-micro-library/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/go-zero-micro-notice/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/go-zero-micro-notice/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/go-zero-micro-notice/k8s/k8s-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/go-zero-micro-notice/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/go-zero-micro-notice/k8s/k8s-hpa.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/go-zero-micro-notice/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/hbase/flink-conf.yaml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/hbase/flink.yaml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/hbase/hadoop.env" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/hbase/hadoop.yaml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/hbase/kafka.yaml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/hrdao-guide/docker/default.conf.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/hrdao-guide/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/hrdao-guide/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/hrdao-guide/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/hrdao-guide/k8s/k8s-ingress.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/hrdao-guide/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/java17/java17.iml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/log-service/docker/boot.sh.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/log-service/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/log-service/docker/dockerfile copy.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/log-service/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/log-service/docker/dockerfileopenjdk17.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/log-service/docker/dockerfileopenjdk21.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/log-service/docker/pipeline.groovy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/log-service/k8s/k8s-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/log-service/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/log-service/k8s/k8s-hpa.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/log-service/k8s/k8s-ingress.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/log-service/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/log-service/k8s/k8s-timezone-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/log-service/log-service.iml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/openjdk21/dockerfile21.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/openjdk21/entrypoint.sh" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/operation-center-offline/docker/default.conf.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/operation-center-offline/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/operation-center-offline/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/operation-center-offline/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/operation-center-offline/k8s/k8s-ingress.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/operation-center-offline/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/operation-center/docker/default.conf.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/operation-center/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/operation-center/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/operation-center/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/operation-center/k8s/k8s-ingress.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/operation-center/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/pvc/app-backend-pv.yaml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/pvc/app-backend-pvc.yaml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/pvc/arthas-pv.yaml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/pvc/arthas-pvc.yaml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/pvc/cos-secret.yaml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/resources.iml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/shell/add_security_group_policies.sh" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/shell/monitor_dts.sh" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/shell/monitor_dts_status.go" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/simple-cube-trainer-2nd/docker/default.conf.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/simple-cube-trainer-2nd/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/simple-cube-trainer-2nd/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/simple-cube-trainer-2nd/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/simple-cube-trainer-2nd/k8s/k8s-ingress.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/simple-cube-trainer-2nd/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/simple-cube-trainer/docker/default.conf.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/simple-cube-trainer/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/simple-cube-trainer/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/simple-cube-trainer/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/simple-cube-trainer/k8s/k8s-ingress.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/simple-cube-trainer/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/sphere-account/config/config.yaml.dev" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/sphere-account/config/config.yaml.test" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/sphere-account/config/config.yaml.uat" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/sphere-account/config/registry.yaml.dev" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/sphere-account/config/registry.yaml.test" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/sphere-account/config/registry.yaml.uat" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/sphere-account/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/sphere-account/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/sphere-account/k8s/k8s-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/sphere-account/k8s/k8s-deployment.yaml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/sphere-client/docker/default.conf.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/sphere-client/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/sphere-client/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/sphere-client/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/sphere-client/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/sphere-wislide-battle/config/config.yaml.dev" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/sphere-wislide-battle/config/config.yaml.ggprod" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/sphere-wislide-battle/config/config.yaml.prod" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/sphere-wislide-battle/config/config.yaml.test" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/sphere-wislide-battle/config/config.yaml.uat" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/sphere-wislide-battle/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/sphere-wislide-battle/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/sphere-wislide-battle/k8s/k8s-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/sphere-wislide-battle/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/sphere-wislide-battle/k8s/k8s-hpa.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/sphere-wislide-battle/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/trace-source/docker/default.conf.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/trace-source/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/trace-source/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/trace-source/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/trace-source/k8s/k8s-ingress.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/trace-source/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/user-center-service/docker/boot.sh.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/user-center-service/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/user-center-service/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/user-center-service/docker/pipeline.groovy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/user-center-service/k8s/k8s-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/user-center-service/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/user-center-service/k8s/k8s-hpa.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/user-center-service/k8s/k8s-ingress.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/user-center-service/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/user-center-service/k8s/k8s-timezone-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/user-center-service/user-center-service.iml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/user-global-service/docker/boot.sh.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/user-global-service/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/user-global-service/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/user-global-service/docker/pipeline.groovy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/user-global-service/k8s/k8s-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/user-global-service/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/user-global-service/k8s/k8s-hpa.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/user-global-service/k8s/k8s-ingress.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/user-global-service/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/user-global-service/k8s/k8s-timezone-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/user-global-service/user-global-service.iml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/wislide-battle-service/docker/boot.sh.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/wislide-battle-service/docker/docker-compose.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/wislide-battle-service/docker/dockerfile.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/wislide-battle-service/k8s/k8s-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/wislide-battle-service/k8s/k8s-deployment.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/wislide-battle-service/k8s/k8s-hpa.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/wislide-battle-service/k8s/k8s-ingress.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/wislide-battle-service/k8s/k8s-service.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/resources/wislide-battle-service/k8s/k8s-timezone-configmap.yml.vm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/org/cicd/devops/build.groovy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/org/cicd/devops/config.groovy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/org/cicd/devops/deploy.groovy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/org/cicd/devops/gitServer.groovy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/org/cicd/devops/sonar.groovy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/org/cicd/utils/utils.groovy" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/vars/log.groovy" beforeDir="false" afterPath="$PROJECT_DIR$/vars/log.groovy" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="GOROOT" url="file://C:/Program Files/Go" />
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "customColor": "",
  "associatedIndex": 6
}]]></component>
  <component name="ProjectId" id="302lp9lNJBLLSAO4qMRlRoTU1Zp" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.GoLinterPluginOnboarding": "true",
    "RunOnceActivity.GoLinterPluginStorageMigration": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "RunOnceActivity.go.formatter.settings.were.checked": "true",
    "RunOnceActivity.go.migrated.go.modules.settings": "true",
    "RunOnceActivity.go.modules.go.list.on.any.changes.was.set": "true",
    "git-widget-placeholder": "old",
    "go.import.settings.migrated": "true",
    "go.sdk.automatically.set": "true",
    "last_opened_file_path": "C:/Users/<USER>",
    "node.js.detected.package.eslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "nodejs_package_manager_path": "npm"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-gosdk-3b128438d3f6-4b567d62c776-org.jetbrains.plugins.go.sharedIndexes.bundled-GO-251.26094.127" />
        <option value="bundled-js-predefined-d6986cc7102b-b26f3e71634d-JavaScript-GO-251.26094.127" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="37616c9f-28cf-41a9-abf6-378d70091c6e" name="Changes" comment="" />
      <created>1752833738254</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752833738254</updated>
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VgoProject">
    <settings-migrated>true</settings-migrated>
  </component>
</project>