apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${appName}
  namespace: ${namespace}
  labels:
    app: ${appName}
  annotations:
    # HPA 自动扩缩容建议：副本数由 HPA 控制
spec:
  # replicas 字段由 HPA 控制，无需在此指定
  selector:
    matchLabels:
      app: ${appName}
  template:
    metadata:
      labels:
        app: ${appName}
    spec:
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - ${appName}
                topologyKey: "kubernetes.io/hostname"
      containers:
        - name: ${appName}
          image: ${imageName}:${tag}
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: ${port}
          env:
            - name: ENV
              value: "${env}"
          resources:
            requests:
              memory: "128Mi"
              cpu: "100m"
            limits:
              memory: "512Mi"
              cpu: "500m"
          livenessProbe:
            httpGet:
              path: /healthz
              port: ${port}
            initialDelaySeconds: 10
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /healthz
              port: ${port}
            initialDelaySeconds: 5
            periodSeconds: 5
          volumeMounts:
            - name: app-config
              mountPath: /app/config
          command: ["./app"]
      volumes:
        - name: app-config
          configMap:
            name: ${appName}-config
      imagePullSecrets:
        - name: regcred
