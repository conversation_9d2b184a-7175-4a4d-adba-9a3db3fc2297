/**
 * 简单的测试运行器
 * 用于验证Jenkins共享库的核心功能
 */

// 模拟Jenkins环境变量
env = [
    BUILD_NUMBER: '123',
    JOB_NAME: 'test-job',
    WORKSPACE: '/tmp/test'
]

// 模拟Jenkins方法
def echo(message) {
    println "[ECHO] ${message}"
}

def sh(command) {
    println "[SH] ${command}"
    return "mock-output"
}

def fileExists(path) {
    // 模拟一些文件存在
    def existingFiles = ['pom.xml', 'Dockerfile', 'k8s']
    return existingFiles.contains(path)
}

def writeFile(args) {
    println "[WRITE_FILE] ${args.file}: ${args.text?.take(100)}..."
}

def readFile(path) {
    return "mock template content with \${appName} and \${namespace}"
}

def pwd() {
    return "/tmp/test"
}

// 测试配置管理器
def testConfigManager() {
    println "\n=== 测试配置管理器 ==="
    
    try {
        def config = org.cicd.config.ConfigManager.getConfig([
            imageName: 'test-app',
            appName: 'test',
            port: 8080
        ])
        
        println "✅ 配置管理器测试通过"
        println "   - 环境: ${config.ENV}"
        println "   - 标签: ${config.TAG}"
        println "   - 端口: ${config.port}"
        
    } catch (Exception e) {
        println "❌ 配置管理器测试失败: ${e.message}"
    }
}

// 测试参数验证
def testConfigValidator() {
    println "\n=== 测试参数验证器 ==="
    
    try {
        // 测试有效参数
        org.cicd.utils.ConfigValidator.validateEnvironment('dev')
        org.cicd.utils.ConfigValidator.validateDeployType('k8s')
        org.cicd.utils.ConfigValidator.validateImageName('registry.com/app')
        
        println "✅ 参数验证器测试通过"
        
        // 测试无效参数
        try {
            org.cicd.utils.ConfigValidator.validateEnvironment('invalid')
            println "❌ 应该抛出异常但没有"
        } catch (IllegalArgumentException e) {
            println "✅ 正确捕获无效环境参数"
        }
        
    } catch (Exception e) {
        println "❌ 参数验证器测试失败: ${e.message}"
    }
}

// 测试日志工具
def testLogger() {
    println "\n=== 测试日志工具 ==="
    
    try {
        org.cicd.utils.Logger.info("这是一条信息日志", "TestRunner")
        org.cicd.utils.Logger.warning("这是一条警告日志", "TestRunner")
        org.cicd.utils.Logger.error("这是一条错误日志", "TestRunner")
        org.cicd.utils.Logger.success("这是一条成功日志", "TestRunner")
        org.cicd.utils.Logger.debug("这是一条调试日志", "TestRunner")
        
        println "✅ 日志工具测试通过"
        
    } catch (Exception e) {
        println "❌ 日志工具测试失败: ${e.message}"
    }
}

// 测试基本安全功能（简化版）
def testBasicSecurity() {
    println "\n=== 测试基本安全功能 ==="

    try {
        // 简单的输入验证测试
        def input = "normal-input"
        if (input && !input.contains(";") && !input.contains("|")) {
            println "✅ 基本输入验证通过: ${input}"
        }

        // 简单的敏感信息掩码测试
        def text = "password=secret123&token=abc"
        def masked = text.replaceAll(/password=\w+/, 'password=****')
        println "✅ 简单敏感信息掩码: ${masked}"

        println "✅ 基本安全功能测试通过"

    } catch (Exception e) {
        println "❌ 基本安全功能测试失败: ${e.message}"
    }
}

// 测试模板处理器
def testTemplateProcessor() {
    println "\n=== 测试模板处理器 ==="
    
    try {
        def variables = [
            appName: 'test-app',
            namespace: 'test',
            imageName: 'registry.com/test',
            tag: 'v123',
            port: 8080
        ]
        
        // 模拟模板处理
        def templateContent = "name: \${appName}\nnamespace: \${namespace}\nimage: \${imageName}:\${tag}"
        def result = templateContent
        variables.each { key, value ->
            result = result.replaceAll(/\$\{${key}\}/, value.toString())
        }
        
        println "✅ 模板处理测试通过"
        println "   处理结果: ${result.take(50)}..."
        
    } catch (Exception e) {
        println "❌ 模板处理器测试失败: ${e.message}"
    }
}

// 测试错误处理器
def testErrorHandler() {
    println "\n=== 测试错误处理器 ==="
    
    try {
        // 测试成功操作
        def result = org.cicd.utils.ErrorHandler.executeWithErrorHandling({
            return "success"
        }, "测试操作", 0)
        
        println "✅ 错误处理器测试通过: ${result}"
        
        // 测试安全执行
        def safeResult = org.cicd.utils.ErrorHandler.safeExecute({
            throw new Exception("测试异常")
        }, "安全测试", "默认值")
        
        println "✅ 安全执行测试通过: ${safeResult}"
        
    } catch (Exception e) {
        println "❌ 错误处理器测试失败: ${e.message}"
    }
}

// 运行所有测试
def runAllTests() {
    println "🧪 开始运行Jenkins共享库测试..."
    println "=" * 50
    
    testConfigManager()
    testConfigValidator()
    testLogger()
    testBasicSecurity()
    testTemplateProcessor()
    testErrorHandler()
    
    println "\n" + "=" * 50
    println "🎉 测试运行完成！"
    println "\n💡 提示："
    println "   - 这是一个简化的测试，实际使用时需要在Jenkins环境中运行"
    println "   - 建议在实际项目中添加更详细的单元测试"
    println "   - 可以使用Jenkins Pipeline Unit Test框架进行更完整的测试"
}

// 如果直接运行此脚本
if (this.class.name == 'TestRunner') {
    runAllTests()
}
