# Jenkins共享库使用指南

## 📚 目录
- [环境准备](#环境准备)
- [项目集成](#项目集成)
- [配置说明](#配置说明)
- [部署流程](#部署流程)
- [故障排查](#故障排查)
- [最佳实践](#最佳实践)

## 🔧 环境准备

### 1. Jenkins配置

#### 安装必需插件
```bash
# 必需插件列表
- Pipeline
- Git
- Docker Pipeline
- Kubernetes
- HTTP Request
- Email Extension
```

#### 配置共享库
1. 进入 Jenkins 管理 → 系统配置
2. 找到 "Global Pipeline Libraries"
3. 添加新库：
   - Name: `jenkinslib2`
   - Default version: `main`
   - Retrieval method: `Modern SCM`
   - Source Code Management: `Git`
   - Project Repository: `你的共享库仓库地址`

### 2. 凭据配置

#### Git凭据
```bash
# SSH密钥类型
ID: git-ssh-key
Type: SSH Username with private key
```

#### Docker Registry凭据
```bash
# 用户名密码类型
ID: docker-registry
Type: Username with password
```

#### 通知Webhook凭据
```bash
# 钉钉Webhook
ID: dingtalk-webhook
Type: Secret text

# 企业微信Webhook  
ID: wechat-webhook
Type: Secret text
```

## 🚀 项目集成

### 1. 创建Jenkinsfile

在项目根目录创建 `Jenkinsfile`：

```groovy
@Library('jenkinslib2@main') _

buildAndDeploy(
    repoUrl: '**************:company/project.git',
    imageName: 'registry.company.com/project/app',
    containerName: 'app',
    k8sNamespace: 'production',
    appName: 'my-app'
)
```

### 2. 项目结构要求

#### Java项目
```
project/
├── pom.xml                    # Maven配置文件
├── src/                       # 源代码目录
├── Dockerfile                 # 可选，会自动生成
└── Jenkinsfile               # Pipeline配置
```

#### Vue项目
```
project/
├── package.json              # NPM配置文件
├── src/                      # 源代码目录
├── dist/                     # 构建输出目录
├── Dockerfile                # 可选，会自动生成
└── Jenkinsfile              # Pipeline配置
```

#### Go项目
```
project/
├── go.mod                    # Go模块文件
├── main.go                   # 主程序文件
├── Dockerfile                # 可选，会自动生成
└── Jenkinsfile              # Pipeline配置
```

## ⚙️ 配置说明

### 1. 基础配置

```groovy
buildAndDeploy(
    // Git仓库配置
    repoUrl: '**************:company/project.git',
    credentialsId: 'git-ssh-key',
    
    // 镜像配置
    imageName: 'registry.company.com/project/app',
    containerName: 'app',
    
    // K8s配置
    k8sNamespace: 'production',
    appName: 'my-app',
    port: 8080,
    replicas: 3
)
```

### 2. 高级配置

```groovy
buildAndDeploy(
    // ... 基础配置 ...
    
    // Java特定配置
    javaOpts: '-Xms1g -Xmx2g -Dspring.profiles.active=prod',
    jarFile: 'target/app.jar',
    
    // 通知配置
    notification: [
        dingtalk: [
            enabled: true,
            webhookCredentialsId: 'dingtalk-webhook'
        ],
        email: [
            enabled: true,
            recipients: '<EMAIL>'
        ]
    ],
    
    // 环境变量
    envVars: [
        'TZ': 'Asia/Shanghai',
        'LOG_LEVEL': 'INFO'
    ]
)
```

## 🔄 部署流程

### 1. 标准部署流程

```mermaid
graph TD
    A[开始] --> B[初始化配置]
    B --> C[拉取代码]
    C --> D[配置替换]
    D --> E[项目构建]
    E --> F[运行测试]
    F --> G[构建镜像]
    G --> H[部署应用]
    H --> I[发送通知]
    I --> J[结束]
```

### 2. 灰度发布流程

1. 选择 `GRAY_RELEASE = true`
2. 系统会先部署少量实例
3. 验证无误后进行全量部署

### 3. 回滚流程

1. 选择 `ROLLBACK = true`
2. 系统会自动回滚到上一个版本
3. 等待回滚完成并验证

## 🔍 故障排查

### 1. 常见问题

#### 构建失败
```bash
# 检查项目类型识别
- 确保项目根目录有 pom.xml、package.json 或 go.mod
- 检查文件权限和路径

# 检查依赖安装
- Java: Maven/Gradle配置
- Vue: Node.js版本和NPM配置
- Go: Go版本和模块配置
```

#### 镜像构建失败
```bash
# 检查Dockerfile
- 确保Dockerfile语法正确
- 检查基础镜像是否可访问
- 验证构建上下文

# 检查Docker服务
- 确保Jenkins节点有Docker权限
- 检查Docker daemon状态
```

#### K8s部署失败
```bash
# 检查集群连接
kubectl cluster-info

# 检查命名空间
kubectl get namespaces

# 检查资源配额
kubectl describe quota -n <namespace>

# 查看Pod状态
kubectl get pods -n <namespace>
kubectl describe pod <pod-name> -n <namespace>
```

### 2. 日志分析

#### 查看构建日志
- Jenkins控制台输出包含详细的彩色日志
- 每个阶段都有明确的开始和结束标记
- 错误信息会高亮显示

#### 查看应用日志
```bash
# K8s应用日志
kubectl logs -f deployment/<app-name> -n <namespace>

# Docker容器日志
docker logs -f <container-name>
```

## 💡 最佳实践

### 1. 安全实践

```groovy
// ✅ 推荐：使用凭据管理
credentialsId: 'git-ssh-key'

// ❌ 避免：硬编码敏感信息
// repoUrl: 'https://user:<EMAIL>/repo.git'
```

### 2. 性能优化

```groovy
// ✅ 推荐：合理设置资源限制
replicas: 3,
javaOpts: '-Xms1g -Xmx2g'

// ✅ 推荐：启用测试缓存
// 在项目中配置Maven/NPM缓存
```

### 3. 监控和通知

```groovy
// ✅ 推荐：配置多渠道通知
notification: [
    dingtalk: [enabled: true, webhookCredentialsId: 'dingtalk'],
    email: [enabled: true, recipients: '<EMAIL>']
]
```

### 4. 环境管理

```groovy
// ✅ 推荐：明确环境配置
// 开发环境
ENV: 'dev'
replicas: 1
javaOpts: '-Xms256m -Xmx512m'

// 生产环境  
ENV: 'prod'
replicas: 3
javaOpts: '-Xms1g -Xmx2g'
```

### 5. 版本管理

```bash
# ✅ 推荐：使用语义化版本
@Library('jenkinslib2@v2.0.0') _

# ✅ 推荐：固定版本避免意外变更
@Library('jenkinslib2@main') _  # 开发环境
@Library('jenkinslib2@v1.0.0') _  # 生产环境
```

## 📞 技术支持

如果遇到问题，请：

1. 查看Jenkins控制台日志
2. 检查本文档的故障排查部分
3. 联系DevOps团队
4. 提交Issue到共享库仓库

---

📝 **文档版本**: v2.0  
🕐 **更新时间**: 2024-12-10  
👥 **维护团队**: DevOps Team
