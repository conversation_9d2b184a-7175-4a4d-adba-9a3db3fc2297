def call(Map params) {
    if (params.DEPLOY_TYPE == 'docker') {
        echo '灰度发布：先启动一台新容器，验证后再全量发布'
        sh "docker run -d --name ${params.containerName}_gray ${params.imageName}:${params.TAG}"
        // 这里可以加验证逻辑
        // 验证通过后全量发布
        sh "docker stop ${params.containerName}_gray && docker rm ${params.containerName}_gray"
        sh "docker run -d --name ${params.containerName} ${params.imageName}:${params.TAG}"
    } else if (params.DEPLOY_TYPE == 'docker-compose') {
        echo '灰度发布：先启动部分服务，验证后全量'
        sh 'docker-compose up -d'
    } else if (params.DEPLOY_TYPE == 'k8s') {
        echo '灰度发布：先滚动更新部分 pod，验证后全量'
        sh 'kubectl rollout status deployment/$(kubectl get deploy -o jsonpath="{.items[0].metadata.name}") -n ${params.k8sNamespace ?: "default"}'
    } else {
        echo '未知部署类型，无法灰度发布'
    }
} 