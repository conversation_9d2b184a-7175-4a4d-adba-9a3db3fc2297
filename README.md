jenkinslib2/ (优化后)
  ├── vars/
  │   ├── buildAndDeploy.groovy      # 主入口，参数化调用
  │   ├── envConfig.groovy           # 环境变量和配置处理
  │   ├── rollback.groovy            # 回滚逻辑
  │   ├── grayRelease.groovy         # 灰度发布逻辑
  │   └── log.groovy                 # 日志工具
  ├── src/org/cicd/
  │   ├── DeployJava.groovy          # Java项目构建部署
  │   ├── DeployVue.groovy           # Vue项目构建部署
  │   ├── DeployGo.groovy            # Go项目构建部署
  │   ├── DockerUtil.groovy          # Docker操作工具
  │   ├── K8sUtil.groovy             # Kubernetes操作工具
  │   ├── Notify.groovy              # 多渠道通知工具
  │   ├── gitServer.groovy           # Git操作工具
  │   ├── config/
  │   │   └── ConfigManager.groovy   # 统一配置管理
  │   ├── utils/
  │   │   ├── Logger.groovy          # 结构化日志工具
  │   │   ├── ErrorHandler.groovy    # 错误处理和重试
  │   │   ├── ConfigValidator.groovy # 参数验证工具
  │   │   └── GitUtils.groovy        # Git操作工具
  │   ├── security/
  │   │   └── SecurityUtils.groovy   # 安全工具类
  │   └── template/
  │       └── TemplateProcessor.groovy # 模板处理工具
  ├── resources/templates/
  │   ├── dockerfile-java.vm         # Java Dockerfile模板
  │   ├── dockerfile-vue.vm          # Vue Dockerfile模板
  │   ├── dockerfile-go.vm           # Go Dockerfile模板
  │   ├── k8s-deployment-java.yml.vm # Java K8s部署模板
  │   ├── k8s-deployment-vue.yml.vm  # Vue K8s部署模板
  │   └── k8s-deployment-go.yml.vm   # Go K8s部署模板
  ├── docs/
  │   └── README.md
  └── Jenkinsfile (示例)

