# Jenkins 通用 Pipeline 共享库 v2.0 (优化版)

## 🚀 功能特性

### 核心功能
- ✅ **多环境支持**：dev/test/prod/gray 环境参数化部署
- ✅ **多语言支持**：Java、Vue、Go 项目自动识别与构建
- ✅ **多部署方式**：Docker、Docker Compose、Kubernetes
- ✅ **灰度发布**：支持灰度发布与一键回滚
- ✅ **自动标签**：格式 `vyyyyMMddHHmmss_BUILD_NUMBER`
- ✅ **模板系统**：自动处理 Dockerfile 和 K8s 配置模板
- ✅ **多渠道通知**：钉钉、企业微信、邮件、Slack

### 新增优化功能 🆕
- 🔧 **统一配置管理**：集中化的配置处理和验证
- 📝 **结构化日志**：彩色输出、分级日志、调试信息
- 🔄 **智能错误处理**：自动重试、异常恢复、详细诊断
- 📋 **模板处理**：自动变量替换、多项目类型支持
- ⚡ **性能优化**：构建缓存、并行处理、资源清理
- 🎯 **节点选择**：动态选择构建节点，支持多种环境
- 🛡️ **异常处理**：全局异常捕获、恢复建议、资源清理
- ✅ **参数验证**：全面的输入验证和默认值处理

## 📁 项目结构

```
jenkinslib2/ (优化后)
  ├── vars/                          # Pipeline全局函数
  │   ├── buildAndDeploy.groovy      # 主入口，参数化调用
  │   ├── getConfig.groovy           # 配置获取函数
  │   ├── sendNotification.groovy    # 通知发送函数
  │   ├── envConfig.groovy           # 环境变量和配置处理
  │   ├── rollback.groovy            # 回滚逻辑
  │   ├── grayRelease.groovy         # 灰度发布逻辑
  │   └── log.groovy                 # 日志工具
  ├── src/org/cicd/                  # 核心类库
  │   ├── DeployJava.groovy          # Java项目构建部署
  │   ├── DeployVue.groovy           # Vue项目构建部署
  │   ├── DeployGo.groovy            # Go项目构建部署
  │   ├── DockerUtil.groovy          # Docker操作工具
  │   ├── K8sUtil.groovy             # Kubernetes操作工具
  │   ├── Notify.groovy              # 多渠道通知工具
  │   ├── gitServer.groovy           # Git操作工具
  │   ├── config/
  │   │   └── ConfigManager.groovy   # 统一配置管理
  │   ├── utils/
  │   │   ├── Logger.groovy          # 结构化日志工具
  │   │   ├── ErrorHandler.groovy    # 错误处理和重试
  │   │   ├── ExceptionHandler.groovy # 全局异常处理
  │   │   ├── PerformanceOptimizer.groovy # 性能优化工具
  │   │   ├── ConfigValidator.groovy # 参数验证工具
  │   │   └── GitUtils.groovy        # Git操作工具
  │   └── template/
  │       └── TemplateProcessor.groovy # 模板处理工具
  ├── resources/templates/           # 模板文件
  │   ├── dockerfile-java.vm         # Java Dockerfile模板
  │   ├── dockerfile-vue.vm          # Vue Dockerfile模板
  │   ├── dockerfile-go.vm           # Go Dockerfile模板
  │   ├── k8s-deployment-java.yml.vm # Java K8s部署模板
  │   ├── k8s-deployment-vue.yml.vm  # Vue K8s部署模板
  │   └── k8s-deployment-go.yml.vm   # Go K8s部署模板
  ├── docs/                         # 文档
  │   ├── README.md                 # 详细使用文档
  │   ├── USAGE_GUIDE.md           # 使用指南
  │   └── NODE_SELECTION_GUIDE.md  # 节点选择指南
  ├── test/                        # 测试文件
  │   └── TestRunner.groovy        # 测试运行器
  └── Jenkinsfile                  # 示例Pipeline
```

