jenkinslib/
  ├── vars/
  │   ├── buildAndDeploy.groovy      # 主入口，参数化调用
  │   ├── envConfig.groovy           # 环境变量和配置处理
  │   ├── rollback.groovy            # 回滚逻辑
  │   ├── grayRelease.groovy         # 灰度发布逻辑
  │   └── log.groovy                 # 日志工具
  ├── src/
  │   └── org/
  │       └── cicd/
  │           ├── DeployJava.groovy
  │           ├── DeployVue.groovy
  │           ├── DeployGo.groovy
  │           ├── DockerUtil.groovy
  │           ├── K8sUtil.groovy
  │           └── Notify.groovy
  ├── resources/
  │   └── templates/
  │       ├── docker-compose.yml.vm
  │       ├── k8s-deployment.yml.vm
  │       └── config-template.vm
  ├── docs/
  │   └── README.md
  └── Jenkinsfile (示例)

