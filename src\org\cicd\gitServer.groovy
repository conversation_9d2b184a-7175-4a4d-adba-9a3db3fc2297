package org.cicd

import org.cicd.utils.Logger
import org.cicd.utils.GitUtils

/**
 * Git服务器操作类
 * 提供代码检出和相关Git操作
 * @deprecated 建议直接使用 GitUtils 类
 */
class GitServer implements Serializable {

    /**
     * 检出项目代码
     * @param mapParams 参数映射，包含 repository_url 和 branch_name
     */
    static def checkOutCode(Map mapParams) {
        Logger.info("开始检出项目代码", "GitServer")

        def repoUrl = mapParams.get("repository_url")
        def branchName = mapParams.get("branch_name") ?: params.branch_name
        def credentialsId = mapParams.get("credentialsId") ?: 'root_131_new'

        GitUtils.checkout(repoUrl, branchName, credentialsId)

        // TODO: 初始化包信息
        // getGav(params)
    }

    /**
     * 检出代码 - 兼容性方法
     * @deprecated 使用 GitUtils.checkout() 替代
     */
    static def checkOut(String srcUrl, String branchName) {
        GitUtils.checkout(srcUrl, branchName, 'root_131_new')
    }
}

