package org.cicd.utils

/**
 * 性能优化工具类
 * 提供构建和部署过程的性能优化功能
 */
class PerformanceOptimizer implements Serializable {
    
    /**
     * 优化Docker构建
     * @param config 配置对象
     */
    static def optimizeDockerBuild(Map config) {
        Logger.info("开始Docker构建优化", "PerformanceOptimizer")
        
        // 启用Docker BuildKit
        enableDockerBuildKit()
        
        // 清理不必要的镜像和容器
        cleanupDockerResources()
        
        // 优化Dockerfile缓存
        optimizeDockerfileCache(config)
        
        Logger.success("Docker构建优化完成", "PerformanceOptimizer")
    }
    
    /**
     * 优化Maven构建
     * @param config 配置对象
     */
    static def optimizeMavenBuild(Map config) {
        Logger.info("开始Maven构建优化", "PerformanceOptimizer")
        
        // 启用并行构建
        def parallelArgs = "-T 1C"
        
        // 启用离线模式（如果依赖已下载）
        def offlineArgs = ""
        if (config.MAVEN_OFFLINE == true) {
            offlineArgs = "-o"
        }
        
        // 跳过不必要的插件
        def skipArgs = "-Dmaven.javadoc.skip=true -Dmaven.source.skip=true"
        
        def optimizedArgs = "${parallelArgs} ${offlineArgs} ${skipArgs}".trim()
        Logger.info("Maven优化参数: ${optimizedArgs}", "PerformanceOptimizer")
        
        return optimizedArgs
    }
    
    /**
     * 优化NPM构建
     * @param config 配置对象
     */
    static def optimizeNpmBuild(Map config) {
        Logger.info("开始NPM构建优化", "PerformanceOptimizer")
        
        // 检查并使用缓存
        if (fileExists('node_modules')) {
            Logger.info("发现node_modules缓存，跳过依赖安装", "PerformanceOptimizer")
            return false // 不需要重新安装
        }
        
        // 使用npm ci替代npm install（更快且可靠）
        def useNpmCi = fileExists('package-lock.json')
        if (useNpmCi) {
            Logger.info("使用npm ci进行快速安装", "PerformanceOptimizer")
            return "ci"
        }
        
        return "install"
    }
    
    /**
     * 优化Go构建
     * @param config 配置对象
     */
    static def optimizeGoBuild(Map config) {
        Logger.info("开始Go构建优化", "PerformanceOptimizer")
        
        // 启用Go模块代理
        def goProxy = config.GO_PROXY ?: "https://proxy.golang.org,direct"
        
        // 启用构建缓存
        def buildArgs = "-buildmode=default"
        
        // 并行构建
        def parallelArgs = "-p ${Runtime.runtime.availableProcessors()}"
        
        def optimizedArgs = "${buildArgs} ${parallelArgs}".trim()
        Logger.info("Go优化参数: ${optimizedArgs}", "PerformanceOptimizer")
        
        return [
            goProxy: goProxy,
            buildArgs: optimizedArgs
        ]
    }
    
    /**
     * 启用Docker BuildKit
     */
    private static def enableDockerBuildKit() {
        try {
            // 检查是否已启用BuildKit
            def buildkitEnabled = ErrorHandler.executeShell("docker version --format '{{.Server.Experimental}}'", "检查BuildKit", true, true)
            
            if (buildkitEnabled != "true") {
                Logger.info("启用Docker BuildKit", "PerformanceOptimizer")
                // 设置环境变量启用BuildKit
                env.DOCKER_BUILDKIT = "1"
            } else {
                Logger.info("Docker BuildKit已启用", "PerformanceOptimizer")
            }
        } catch (Exception e) {
            Logger.warning("无法检查或启用Docker BuildKit: ${e.message}", "PerformanceOptimizer")
        }
    }
    
    /**
     * 清理Docker资源
     */
    private static def cleanupDockerResources() {
        Logger.info("清理Docker资源", "PerformanceOptimizer")
        
        // 清理悬空镜像
        ErrorHandler.safeExecute({
            ErrorHandler.executeShell("docker image prune -f", "清理悬空镜像", false, true)
        }, "清理Docker悬空镜像")
        
        // 清理停止的容器
        ErrorHandler.safeExecute({
            ErrorHandler.executeShell("docker container prune -f", "清理停止的容器", false, true)
        }, "清理Docker容器")
        
        // 清理未使用的网络
        ErrorHandler.safeExecute({
            ErrorHandler.executeShell("docker network prune -f", "清理未使用的网络", false, true)
        }, "清理Docker网络")
    }
    
    /**
     * 优化Dockerfile缓存
     */
    private static def optimizeDockerfileCache(Map config) {
        if (!fileExists('Dockerfile')) {
            return
        }
        
        Logger.info("分析Dockerfile缓存优化", "PerformanceOptimizer")
        
        try {
            def dockerfile = readFile('Dockerfile')
            def suggestions = []
            
            // 检查COPY指令的顺序
            if (dockerfile.contains('COPY . .') && dockerfile.indexOf('COPY . .') < dockerfile.indexOf('RUN')) {
                suggestions.add("建议将COPY . .指令放在RUN指令之后以优化缓存")
            }
            
            // 检查是否使用了.dockerignore
            if (!fileExists('.dockerignore')) {
                suggestions.add("建议创建.dockerignore文件以减少构建上下文")
            }
            
            // 检查多阶段构建
            if (!dockerfile.contains('FROM') || dockerfile.count('FROM') == 1) {
                suggestions.add("考虑使用多阶段构建以减少最终镜像大小")
            }
            
            if (suggestions) {
                Logger.info("Dockerfile优化建议:", "PerformanceOptimizer")
                suggestions.each { suggestion ->
                    Logger.info("  - ${suggestion}", "PerformanceOptimizer")
                }
            }
            
        } catch (Exception e) {
            Logger.warning("无法分析Dockerfile: ${e.message}", "PerformanceOptimizer")
        }
    }
    
    /**
     * 检查并优化构建缓存
     * @param projectType 项目类型
     * @param config 配置对象
     */
    static def optimizeBuildCache(String projectType, Map config) {
        Logger.info("优化${projectType}项目构建缓存", "PerformanceOptimizer")
        
        switch (projectType) {
            case 'java':
                optimizeMavenCache(config)
                break
            case 'vue':
                optimizeNpmCache(config)
                break
            case 'go':
                optimizeGoCache(config)
                break
            default:
                Logger.info("未知项目类型，跳过缓存优化", "PerformanceOptimizer")
        }
    }
    
    /**
     * 优化Maven缓存
     */
    private static def optimizeMavenCache(Map config) {
        def m2Dir = "${env.HOME}/.m2/repository"
        
        if (fileExists(m2Dir)) {
            Logger.info("发现Maven本地仓库缓存", "PerformanceOptimizer")
            
            // 检查缓存大小
            ErrorHandler.safeExecute({
                def size = ErrorHandler.executeShell("du -sh ${m2Dir}", "检查Maven缓存大小", true, true)
                Logger.info("Maven缓存大小: ${size}", "PerformanceOptimizer")
            }, "检查Maven缓存")
        }
    }
    
    /**
     * 优化NPM缓存
     */
    private static def optimizeNpmCache(Map config) {
        // 检查npm缓存
        ErrorHandler.safeExecute({
            def cacheInfo = ErrorHandler.executeShell("npm cache verify", "验证NPM缓存", true, true)
            Logger.info("NPM缓存信息: ${cacheInfo}", "PerformanceOptimizer")
        }, "检查NPM缓存")
        
        // 检查node_modules大小
        if (fileExists('node_modules')) {
            ErrorHandler.safeExecute({
                def size = ErrorHandler.executeShell("du -sh node_modules", "检查node_modules大小", true, true)
                Logger.info("node_modules大小: ${size}", "PerformanceOptimizer")
            }, "检查node_modules")
        }
    }
    
    /**
     * 优化Go缓存
     */
    private static def optimizeGoCache(Map config) {
        // 检查Go模块缓存
        ErrorHandler.safeExecute({
            def cacheDir = ErrorHandler.executeShell("go env GOMODCACHE", "获取Go模块缓存目录", true, true)
            if (cacheDir && fileExists(cacheDir.trim())) {
                def size = ErrorHandler.executeShell("du -sh ${cacheDir.trim()}", "检查Go缓存大小", true, true)
                Logger.info("Go模块缓存大小: ${size}", "PerformanceOptimizer")
            }
        }, "检查Go缓存")
    }
    
    /**
     * 生成性能报告
     * @param startTime 开始时间
     * @param stage 阶段名称
     */
    static def generatePerformanceReport(long startTime, String stage) {
        def endTime = System.currentTimeMillis()
        def duration = endTime - startTime
        def durationSeconds = duration / 1000.0
        
        Logger.info("=== 性能报告 ===", "PerformanceOptimizer")
        Logger.info("阶段: ${stage}", "PerformanceOptimizer")
        Logger.info("耗时: ${durationSeconds}秒", "PerformanceOptimizer")
        Logger.info("开始时间: ${new Date(startTime)}", "PerformanceOptimizer")
        Logger.info("结束时间: ${new Date(endTime)}", "PerformanceOptimizer")
        Logger.info("================", "PerformanceOptimizer")
        
        return [
            stage: stage,
            duration: duration,
            durationSeconds: durationSeconds,
            startTime: startTime,
            endTime: endTime
        ]
    }
}
