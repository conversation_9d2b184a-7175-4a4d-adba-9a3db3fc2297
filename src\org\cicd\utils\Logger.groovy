package org.cicd.utils

/**
 * 统一日志工具类
 * 提供结构化的日志输出和不同级别的日志记录
 */
class Logger implements Serializable {
    
    private static final String ANSI_RESET = "\u001B[0m"
    private static final String ANSI_RED = "\u001B[31m"
    private static final String ANSI_GREEN = "\u001B[32m"
    private static final String ANSI_YELLOW = "\u001B[33m"
    private static final String ANSI_BLUE = "\u001B[34m"
    private static final String ANSI_PURPLE = "\u001B[35m"
    private static final String ANSI_CYAN = "\u001B[36m"
    
    /**
     * 输出信息级别日志
     */
    static def info(String message, String component = "") {
        def timestamp = new Date().format('yyyy-MM-dd HH:mm:ss')
        def prefix = component ? "[${component}]" : ""
        echo "${ANSI_BLUE}[INFO]${ANSI_RESET} ${timestamp} ${prefix} ${message}"
    }
    
    /**
     * 输出警告级别日志
     */
    static def warning(String message, String component = "") {
        def timestamp = new Date().format('yyyy-MM-dd HH:mm:ss')
        def prefix = component ? "[${component}]" : ""
        echo "${ANSI_YELLOW}[WARN]${ANSI_RESET} ${timestamp} ${prefix} ${message}"
    }
    
    /**
     * 输出错误级别日志
     */
    static def error(String message, String component = "") {
        def timestamp = new Date().format('yyyy-MM-dd HH:mm:ss')
        def prefix = component ? "[${component}]" : ""
        echo "${ANSI_RED}[ERROR]${ANSI_RESET} ${timestamp} ${prefix} ${message}"
    }
    
    /**
     * 输出成功信息
     */
    static def success(String message, String component = "") {
        def timestamp = new Date().format('yyyy-MM-dd HH:mm:ss')
        def prefix = component ? "[${component}]" : ""
        echo "${ANSI_GREEN}[SUCCESS]${ANSI_RESET} ${timestamp} ${prefix} ${message}"
    }
    
    /**
     * 输出调试信息
     */
    static def debug(String message, String component = "") {
        def timestamp = new Date().format('yyyy-MM-dd HH:mm:ss')
        def prefix = component ? "[${component}]" : ""
        echo "${ANSI_CYAN}[DEBUG]${ANSI_RESET} ${timestamp} ${prefix} ${message}"
    }
    
    /**
     * 输出阶段开始信息
     */
    static def stageStart(String stageName) {
        echo "${ANSI_PURPLE}========================================${ANSI_RESET}"
        echo "${ANSI_PURPLE}开始执行阶段: ${stageName}${ANSI_RESET}"
        echo "${ANSI_PURPLE}========================================${ANSI_RESET}"
    }
    
    /**
     * 输出阶段结束信息
     */
    static def stageEnd(String stageName) {
        echo "${ANSI_GREEN}========================================${ANSI_RESET}"
        echo "${ANSI_GREEN}阶段完成: ${stageName}${ANSI_RESET}"
        echo "${ANSI_GREEN}========================================${ANSI_RESET}"
    }
    
    /**
     * 输出分隔线
     */
    static def separator() {
        echo "----------------------------------------"
    }
}
