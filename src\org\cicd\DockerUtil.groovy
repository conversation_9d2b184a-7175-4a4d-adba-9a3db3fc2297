package org.cicd

import org.cicd.utils.Logger
import org.cicd.utils.ErrorHandler
import org.cicd.utils.ConfigValidator
import org.cicd.template.TemplateProcessor

/**
 * Docker操作工具类
 * 提供Docker镜像构建、容器管理等功能
 */
class DockerUtil implements Serializable {

    /**
     * 构建Docker镜像
     */
    static def buildImage(Map params) {
        Logger.stageStart("Docker镜像构建")

        try {
            // 参数验证
            ConfigValidator.validateRequiredParams(params, ['imageName'])
            ConfigValidator.validateImageName(params.imageName)

            def tag = params.TAG ?: "v${new Date().format('yyyyMMddHHmmss')}_${env.BUILD_NUMBER}"
            def fullImageName = "${params.imageName}:${tag}"

            Logger.info("构建Docker镜像: ${fullImageName}", "DockerUtil")

            // 处理Dockerfile模板（如果不存在Dockerfile）
            if (!fileExists('Dockerfile')) {
                Logger.info("Dockerfile不存在，尝试从模板生成", "DockerUtil")
                TemplateProcessor.processDockerTemplates(params)
            }

            // 检查Dockerfile是否存在
            ErrorHandler.requireFileExists('Dockerfile', 'Dockerfile文件不存在，无法构建镜像')

            // 构建镜像
            ErrorHandler.executeWithErrorHandling({
                ErrorHandler.executeShell("docker build -t ${fullImageName} .", "Docker镜像构建")
            }, "Docker镜像构建", 2, 10)

            Logger.success("Docker镜像构建完成: ${fullImageName}", "DockerUtil")
            return fullImageName

        } catch (Exception e) {
            Logger.error("Docker镜像构建失败: ${e.message}", "DockerUtil")
            throw e
        } finally {
            Logger.stageEnd("Docker镜像构建")
        }
    }

    /**
     * 运行Docker容器
     */
    static def runContainer(Map params) {
        Logger.stageStart("Docker容器启动")

        try {
            // 参数验证
            ConfigValidator.validateRequiredParams(params, ['imageName', 'containerName'])
            ConfigValidator.validateImageName(params.imageName)
            ConfigValidator.validateContainerName(params.containerName)

            def tag = params.TAG ?: "v${new Date().format('yyyyMMddHHmmss')}_${env.BUILD_NUMBER}"
            def fullImageName = "${params.imageName}:${tag}"

            Logger.info("启动Docker容器: ${params.containerName}", "DockerUtil")

            // 停止并删除同名容器（如果存在）
            stopAndRemoveContainer(params.containerName)

            // 构建运行命令
            def runCommand = buildRunCommand(params, fullImageName)

            // 启动容器
            ErrorHandler.executeWithErrorHandling({
                ErrorHandler.executeShell(runCommand, "Docker容器启动")
            }, "Docker容器启动", 2, 5)

            Logger.success("Docker容器启动成功: ${params.containerName}", "DockerUtil")

        } catch (Exception e) {
            Logger.error("Docker容器启动失败: ${e.message}", "DockerUtil")
            throw e
        } finally {
            Logger.stageEnd("Docker容器启动")
        }
    }

    /**
     * 使用Docker Compose启动服务
     */
    static def composeUp(Map params) {
        Logger.stageStart("Docker Compose启动")

        try {
            ErrorHandler.requireFileExists('docker-compose.yml', 'docker-compose.yml文件不存在')

            Logger.info("启动Docker Compose服务", "DockerUtil")

            ErrorHandler.executeWithErrorHandling({
                ErrorHandler.executeShell("docker-compose up -d", "Docker Compose启动")
            }, "Docker Compose启动", 2, 10)

            Logger.success("Docker Compose服务启动成功", "DockerUtil")

        } catch (Exception e) {
            Logger.error("Docker Compose启动失败: ${e.message}", "DockerUtil")
            throw e
        } finally {
            Logger.stageEnd("Docker Compose启动")
        }
    }

    /**
     * 停止Docker Compose服务
     */
    static def composeDown(Map params) {
        Logger.stageStart("Docker Compose停止")

        try {
            Logger.info("停止Docker Compose服务", "DockerUtil")

            ErrorHandler.executeWithErrorHandling({
                ErrorHandler.executeShell("docker-compose down", "Docker Compose停止")
            }, "Docker Compose停止", 1, 5)

            Logger.success("Docker Compose服务停止成功", "DockerUtil")

        } catch (Exception e) {
            Logger.error("Docker Compose停止失败: ${e.message}", "DockerUtil")
            throw e
        } finally {
            Logger.stageEnd("Docker Compose停止")
        }
    }

    /**
     * 停止并删除容器
     */
    private static def stopAndRemoveContainer(String containerName) {
        ErrorHandler.safeExecute({
            ErrorHandler.executeShell("docker stop ${containerName}", "停止容器")
        }, "停止容器 ${containerName}")

        ErrorHandler.safeExecute({
            ErrorHandler.executeShell("docker rm ${containerName}", "删除容器")
        }, "删除容器 ${containerName}")
    }

    /**
     * 构建Docker运行命令
     */
    private static def buildRunCommand(Map params, String fullImageName) {
        def command = "docker run -d --name ${params.containerName}"

        // 添加端口映射
        if (params.port) {
            command += " -p ${params.port}:${params.port}"
        }

        // 添加环境变量
        if (params.ENV) {
            command += " -e ENV=${params.ENV}"
        }

        // 添加自定义环境变量
        if (params.envVars) {
            params.envVars.each { key, value ->
                command += " -e ${key}=${value}"
            }
        }

        command += " ${fullImageName}"
        return command
    }
}