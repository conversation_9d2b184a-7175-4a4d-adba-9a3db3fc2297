@Library('jenkinslib2@old') _

// 你可以根据实际项目类型和需求调整参数
buildAndDeploy(
    repoUrl: '*****************:group/project.git', // 项目git地址
    imageName: 'your-registry/namespace/app',       // 镜像名
    containerName: 'app',                           // 容器名（docker用）
    k8sNamespace: 'default',                        // k8s命名空间
    port: 8080,                                     // 应用端口（如需传递到模板）
    appName: 'app',                                 // 应用名（k8s用）
    replicas: 2,                                    // 副本数
    // 其它自定义参数
)