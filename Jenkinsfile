@Library('jenkinslib2@main') _

// 完整的配置示例 - 你可以根据实际项目类型和需求调整参数
buildAndDeploy(
    // === 基本配置 ===
    repoUrl: '*****************:group/project.git', // 项目git地址
    credentialsId: 'git-credentials',                // Git凭据ID

    // === 镜像配置 ===
    imageName: 'your-registry/namespace/app',        // 镜像名
    containerName: 'app',                            // 容器名（docker用）

    // === K8s配置 ===
    k8sNamespace: 'default',                         // k8s命名空间
    appName: 'app',                                  // 应用名（k8s用）
    port: 8080,                                      // 应用端口
    replicas: 2,                                     // 副本数

    // === Java项目特定配置 ===
    javaOpts: '-Xms512m -Xmx1024m -Dspring.profiles.active=prod',

    // === 通知配置 ===
    notification: [
        dingtalk: [
            enabled: true,
            webhookCredentialsId: 'dingtalk-webhook'
        ],
        email: [
            enabled: true,
            recipients: '<EMAIL>'
        ]
    ],

    // === 环境变量 ===
    envVars: [
        'JAVA_TOOL_OPTIONS': '-Dfile.encoding=UTF-8',
        'TZ': 'Asia/Shanghai'
    ]
)