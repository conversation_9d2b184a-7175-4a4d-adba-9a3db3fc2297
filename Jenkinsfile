@Library('jenkinslib2@main') _

pipeline {
    agent any

    // Jenkins参数定义
    parameters {
        choice(
            name: 'DEPLOY_ENV',
            choices: ['dev', 'test', 'prod', 'gray'],
            description: '选择部署环境'
        )
        gitParameter(
            name: 'DEPLOY_BRANCH',
            type: 'PT_BRANCH',
            defaultValue: 'main',
            description: '选择部署分支',
            branchFilter: 'origin/(.*)',
            sortMode: 'DESCENDING_SMART'
        )
        choice(
            name: 'DEPLOY_TYPE',
            choices: ['k8s', 'docker', 'docker-compose'],
            description: '选择部署方式'
        )
        booleanParam(
            name: 'GRAY_RELEASE',
            defaultValue: false,
            description: '是否进行灰度发布'
        )
        booleanParam(
            name: 'ROLLBACK',
            defaultValue: false,
            description: '是否执行回滚操作'
        )
        booleanParam(
            name: 'SKIP_TESTS',
            defaultValue: false,
            description: '是否跳过测试阶段'
        )
    }

    stages {
        stage('参数验证') {
            steps {
                script {
                    echo "=== 部署参数确认 ==="
                    echo "部署环境: ${params.DEPLOY_ENV}"
                    echo "部署分支: ${params.DEPLOY_BRANCH}"
                    echo "部署方式: ${params.DEPLOY_TYPE}"
                    echo "灰度发布: ${params.GRAY_RELEASE}"
                    echo "回滚操作: ${params.ROLLBACK}"
                    echo "跳过测试: ${params.SKIP_TESTS}"
                    echo "========================"
                }
            }
        }

        stage('执行部署') {
            steps {
                script {
                    // 调用共享库的buildAndDeploy函数
                    buildAndDeploy([
                        // === 基本配置 ===
                        repoUrl: '*****************:group/project.git', // 项目git地址
                        credentialsId: 'git-credentials',                // Git凭据ID

                        // === 从Jenkins参数获取的配置 ===
                        ENV: params.DEPLOY_ENV,                         // 部署环境
                        BRANCH_NAME: params.DEPLOY_BRANCH,              // 部署分支
                        DEPLOY_TYPE: params.DEPLOY_TYPE,                // 部署方式
                        GRAY_RELEASE: params.GRAY_RELEASE,              // 灰度发布
                        ROLLBACK: params.ROLLBACK,                      // 回滚操作
                        SKIP_TESTS: params.SKIP_TESTS,                  // 跳过测试

                        // === 镜像配置 ===
                        imageName: 'your-registry/namespace/app',        // 镜像名
                        containerName: 'app',                            // 容器名（docker用）

                        // === K8s配置 ===
                        k8sNamespace: getNamespaceByEnv(params.DEPLOY_ENV), // 根据环境动态设置命名空间
                        appName: 'app',                                  // 应用名（k8s用）
                        port: 8080,                                      // 应用端口
                        replicas: getReplicasByEnv(params.DEPLOY_ENV),   // 根据环境动态设置副本数

                        // === Java项目特定配置 ===
                        javaOpts: getJavaOptsByEnv(params.DEPLOY_ENV),   // 根据环境动态设置JVM参数

                        // === 通知配置 ===
                        notification: [
                            dingtalk: [
                                enabled: true,
                                webhookCredentialsId: 'dingtalk-webhook'
                            ],
                            email: [
                                enabled: true,
                                recipients: '<EMAIL>'
                            ]
                        ],

                        // === 环境变量 ===
                        envVars: [
                            'JAVA_TOOL_OPTIONS': '-Dfile.encoding=UTF-8',
                            'TZ': 'Asia/Shanghai',
                            'DEPLOY_ENV': params.DEPLOY_ENV
                        ]
                    ])
                }
            }
        }
    }

    post {
        always {
            script {
                echo "=== 部署完成 ==="
                echo "环境: ${params.DEPLOY_ENV}"
                echo "分支: ${params.DEPLOY_BRANCH}"
                echo "状态: ${currentBuild.result ?: 'SUCCESS'}"
            }
        }
        success {
            echo "🎉 部署成功！"
        }
        failure {
            echo "❌ 部署失败，请检查日志"
        }
    }
}

// 根据环境获取命名空间
def getNamespaceByEnv(env) {
    switch(env) {
        case 'dev':
            return 'development'
        case 'test':
            return 'testing'
        case 'prod':
            return 'production'
        case 'gray':
            return 'gray-release'
        default:
            return 'default'
    }
}

// 根据环境获取副本数
def getReplicasByEnv(env) {
    switch(env) {
        case 'dev':
            return 1
        case 'test':
            return 1
        case 'prod':
            return 3
        case 'gray':
            return 2
        default:
            return 2
    }
}

// 根据环境获取JVM参数
def getJavaOptsByEnv(env) {
    switch(env) {
        case 'dev':
            return '-Xms256m -Xmx512m -Dspring.profiles.active=dev'
        case 'test':
            return '-Xms512m -Xmx1024m -Dspring.profiles.active=test'
        case 'prod':
            return '-Xms1g -Xmx2g -Dspring.profiles.active=prod'
        case 'gray':
            return '-Xms1g -Xmx2g -Dspring.profiles.active=prod'
        default:
            return '-Xms512m -Xmx1024m'
    }
}