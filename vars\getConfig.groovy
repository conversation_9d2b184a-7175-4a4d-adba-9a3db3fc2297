/**
 * 获取配置对象的全局函数
 * 这个函数可以访问Pipeline上下文中的env变量
 */
def call(Map userParams = [:]) {
    // 收集Jenkins环境变量
    def jenkinsEnv = [
        ENV: env.ENV,
        BRANCH_NAME: env.BRANCH_NAME,
        BUILD_NUMBER: env.BUILD_NUMBER,
        JOB_NAME: env.JOB_NAME,
        WORKSPACE: env.WORKSPACE
    ]
    
    // 调用ConfigManager，传入环境变量
    return org.cicd.config.ConfigManager.getConfig(userParams, jenkinsEnv)
}
