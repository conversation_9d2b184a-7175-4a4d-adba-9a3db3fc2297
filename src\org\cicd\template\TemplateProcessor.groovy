package org.cicd.template

import org.cicd.utils.Logger
import org.cicd.utils.ErrorHandler
import org.cicd.security.SecurityUtils

/**
 * 模板处理工具类
 * 负责处理 .vm 模板文件的变量替换
 */
class TemplateProcessor implements Serializable {
    
    /**
     * 处理模板文件，替换变量并生成最终文件
     * @param templatePath 模板文件路径
     * @param outputPath 输出文件路径
     * @param variables 变量映射
     */
    static def processTemplate(String templatePath, String outputPath, Map variables) {
        Logger.info("处理模板文件: ${templatePath} -> ${outputPath}", "TemplateProcessor")
        
        try {
            // 检查模板文件是否存在
            ErrorHandler.requireFileExists(templatePath, "模板文件不存在: ${templatePath}")
            
            // 读取模板内容
            def templateContent = readFile(templatePath)
            
            // 替换变量
            def processedContent = replaceVariables(templateContent, variables)
            
            // 写入输出文件
            writeFile(file: outputPath, text: processedContent)
            
            Logger.success("模板处理完成: ${outputPath}", "TemplateProcessor")
            
        } catch (Exception e) {
            Logger.error("模板处理失败: ${e.message}", "TemplateProcessor")
            throw e
        }
    }
    
    /**
     * 批量处理模板目录
     * @param templateDir 模板目录
     * @param outputDir 输出目录
     * @param variables 变量映射
     * @param projectType 项目类型 (java, vue, go)
     */
    static def processTemplateDirectory(String templateDir, String outputDir, Map variables, String projectType = null) {
        Logger.stageStart("批量处理模板")
        
        try {
            // 确保输出目录存在
            sh "mkdir -p ${outputDir}"
            
            // 获取模板文件列表
            def templateFiles = getTemplateFiles(templateDir, projectType)
            
            if (templateFiles.isEmpty()) {
                Logger.warning("未找到匹配的模板文件", "TemplateProcessor")
                return
            }
            
            Logger.info("找到 ${templateFiles.size()} 个模板文件", "TemplateProcessor")
            
            // 处理每个模板文件
            templateFiles.each { templateFile ->
                def outputFile = generateOutputFileName(templateFile, outputDir)
                processTemplate(templateFile, outputFile, variables)
            }
            
            Logger.success("批量模板处理完成", "TemplateProcessor")
            
        } catch (Exception e) {
            Logger.error("批量模板处理失败: ${e.message}", "TemplateProcessor")
            throw e
        } finally {
            Logger.stageEnd("批量处理模板")
        }
    }
    
    /**
     * 为K8s部署处理模板
     * @param config 配置对象
     */
    static def processK8sTemplates(Map config) {
        Logger.stageStart("处理K8s模板")
        
        try {
            def projectType = detectProjectType()
            def templateDir = "resources/templates"
            def outputDir = "k8s"
            
            // 准备模板变量
            def templateVars = prepareK8sTemplateVariables(config, projectType)
            
            // 处理模板
            processTemplateDirectory(templateDir, outputDir, templateVars, projectType)
            
            Logger.success("K8s模板处理完成", "TemplateProcessor")
            
        } catch (Exception e) {
            Logger.error("K8s模板处理失败: ${e.message}", "TemplateProcessor")
            throw e
        } finally {
            Logger.stageEnd("处理K8s模板")
        }
    }
    
    /**
     * 为Docker处理模板
     * @param config 配置对象
     */
    static def processDockerTemplates(Map config) {
        Logger.stageStart("处理Docker模板")
        
        try {
            def projectType = detectProjectType()
            def templateFile = "resources/templates/dockerfile-${projectType}.vm"
            def outputFile = "Dockerfile"
            
            if (fileExists(templateFile)) {
                def templateVars = prepareDockerTemplateVariables(config, projectType)
                processTemplate(templateFile, outputFile, templateVars)
            } else {
                Logger.warning("未找到对应的Dockerfile模板: ${templateFile}", "TemplateProcessor")
            }
            
            Logger.success("Docker模板处理完成", "TemplateProcessor")
            
        } catch (Exception e) {
            Logger.error("Docker模板处理失败: ${e.message}", "TemplateProcessor")
            throw e
        } finally {
            Logger.stageEnd("处理Docker模板")
        }
    }
    
    /**
     * 替换模板中的变量
     * @param content 模板内容
     * @param variables 变量映射
     */
    private static def replaceVariables(String content, Map variables) {
        def result = content
        
        variables.each { key, value ->
            // 处理null值
            def safeValue = value ?: ""
            
            // 替换 ${key} 格式的变量
            result = result.replaceAll(/\$\{${key}\}/, safeValue.toString())
            
            Logger.debug("替换变量: \${${key}} -> ${SecurityUtils.maskSensitiveInfo(safeValue.toString())}", "TemplateProcessor")
        }
        
        // 检查是否还有未替换的变量
        def unresolved = result.findAll(/\$\{[^}]+\}/)
        if (unresolved) {
            Logger.warning("发现未替换的变量: ${unresolved}", "TemplateProcessor")
        }
        
        return result
    }
    
    /**
     * 获取模板文件列表
     * @param templateDir 模板目录
     * @param projectType 项目类型
     */
    private static def getTemplateFiles(String templateDir, String projectType) {
        def templateFiles = []
        
        try {
            // 获取所有.vm文件
            def allFiles = sh(script: "find ${templateDir} -name '*.vm' 2>/dev/null || true", returnStdout: true).trim().split('\n')
            
            allFiles.each { file ->
                if (file && fileExists(file)) {
                    // 如果指定了项目类型，只包含相关的模板
                    if (projectType) {
                        if (file.contains("-${projectType}.") || !file.contains("-java.") && !file.contains("-vue.") && !file.contains("-go.")) {
                            templateFiles.add(file)
                        }
                    } else {
                        templateFiles.add(file)
                    }
                }
            }
        } catch (Exception e) {
            Logger.warning("获取模板文件列表失败: ${e.message}", "TemplateProcessor")
        }
        
        return templateFiles
    }
    
    /**
     * 生成输出文件名
     * @param templateFile 模板文件路径
     * @param outputDir 输出目录
     */
    private static def generateOutputFileName(String templateFile, String outputDir) {
        def fileName = templateFile.split('/').last()
        
        // 移除.vm扩展名
        fileName = fileName.replaceAll(/\.vm$/, '')
        
        // 移除项目类型后缀
        fileName = fileName.replaceAll(/-java|-vue|-go/, '')
        
        return "${outputDir}/${fileName}"
    }
    
    /**
     * 准备K8s模板变量
     * @param config 配置对象
     * @param projectType 项目类型
     */
    private static def prepareK8sTemplateVariables(Map config, String projectType) {
        def vars = [:]
        
        // 基本变量
        vars.appName = config.appName ?: config.containerName ?: 'app'
        vars.namespace = config.k8sNamespace ?: 'default'
        vars.imageName = config.imageName
        vars.tag = config.TAG
        vars.port = config.port ?: 8080
        vars.replicas = config.replicas ?: 2
        vars.env = config.ENV ?: 'dev'
        
        // 项目特定变量
        switch (projectType) {
            case 'java':
                vars.javaOpts = config.javaOpts ?: '-Xms512m -Xmx1024m'
                break
            case 'vue':
                vars.nginxConfig = config.nginxConfig ?: '/etc/nginx/nginx.conf'
                break
            case 'go':
                vars.goEnv = config.goEnv ?: 'production'
                break
        }
        
        return vars
    }
    
    /**
     * 准备Docker模板变量
     * @param config 配置对象
     * @param projectType 项目类型
     */
    private static def prepareDockerTemplateVariables(Map config, String projectType) {
        def vars = [:]
        
        vars.port = config.port ?: 8080
        vars.env = config.ENV ?: 'dev'
        
        // 项目特定变量
        switch (projectType) {
            case 'java':
                vars.jarFile = config.jarFile ?: 'app.jar'
                vars.javaOpts = config.javaOpts ?: '-Xms512m -Xmx1024m'
                break
            case 'vue':
                vars.buildDir = config.buildDir ?: 'dist'
                break
            case 'go':
                vars.binaryName = config.binaryName ?: 'app'
                break
        }
        
        return vars
    }
    
    /**
     * 检测项目类型
     */
    private static def detectProjectType() {
        if (fileExists('pom.xml')) {
            return 'java'
        } else if (fileExists('package.json')) {
            return 'vue'
        } else if (fileExists('go.mod')) {
            return 'go'
        } else {
            return 'unknown'
        }
    }
}
