/**
 * 发送通知的全局函数
 * 这个函数可以访问Pipeline上下文中的currentBuild和env变量
 */
def call(Map config) {
    // 收集构建信息
    def buildInfo = [
        BUILD_RESULT: currentBuild.result ?: 'SUCCESS',
        BUILD_DURATION: currentBuild.durationString ?: '未知',
        BUILD_URL: env.BUILD_URL ?: '#'
    ]
    
    // 合并构建信息到配置中
    def configWithBuildInfo = config + buildInfo
    
    // 调用Notify类
    org.cicd.Notify.send(configWithBuildInfo)
}
