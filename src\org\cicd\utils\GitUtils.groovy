package org.cicd.utils

/**
 * Git操作工具类
 * 提供统一的Git操作接口
 */
class GitUtils implements Serializable {
    
    /**
     * 检出代码
     * @param repoUrl Git仓库地址
     * @param branchName 分支名称
     * @param credentialsId 凭据ID
     */
    static def checkout(String repoUrl, String branchName, String credentialsId = null) {
        Logger.info("开始检出代码", "GitUtils")
        Logger.info("仓库地址: ${repoUrl}", "GitUtils")
        Logger.info("分支: ${branchName}", "GitUtils")
        
        try {
            ConfigValidator.validateGitUrl(repoUrl)
            
            def checkoutConfig = [
                $class: 'GitSCM',
                branches: [[name: branchName]],
                doGenerateSubmoduleConfigurations: false,
                extensions: [],
                submoduleCfg: [],
                userRemoteConfigs: [[url: repoUrl]]
            ]
            
            if (credentialsId) {
                checkoutConfig.userRemoteConfigs[0].credentialsId = credentialsId
            }
            
            checkout(checkoutConfig)
            Logger.success("代码检出成功", "GitUtils")
            
        } catch (Exception e) {
            Logger.error("代码检出失败: ${e.message}", "GitUtils")
            throw e
        }
    }
    
    /**
     * 获取当前提交的短哈希值
     */
    static def getShortCommitHash() {
        try {
            def commitHash = sh(script: 'git rev-parse --short HEAD', returnStdout: true).trim()
            Logger.info("当前提交哈希: ${commitHash}", "GitUtils")
            return commitHash
        } catch (Exception e) {
            Logger.warning("无法获取提交哈希: ${e.message}", "GitUtils")
            return "unknown"
        }
    }
    
    /**
     * 获取当前分支名
     */
    static def getCurrentBranch() {
        try {
            def branch = sh(script: 'git rev-parse --abbrev-ref HEAD', returnStdout: true).trim()
            Logger.info("当前分支: ${branch}", "GitUtils")
            return branch
        } catch (Exception e) {
            Logger.warning("无法获取分支名: ${e.message}", "GitUtils")
            return "unknown"
        }
    }
    
    /**
     * 获取最后一次提交信息
     */
    static def getLastCommitMessage() {
        try {
            def message = sh(script: 'git log -1 --pretty=%B', returnStdout: true).trim()
            Logger.info("最后提交信息: ${message}", "GitUtils")
            return message
        } catch (Exception e) {
            Logger.warning("无法获取提交信息: ${e.message}", "GitUtils")
            return "unknown"
        }
    }
    
    /**
     * 创建并推送标签
     */
    static def createAndPushTag(String tagName, String message = null) {
        try {
            Logger.info("创建标签: ${tagName}", "GitUtils")
            
            def tagMessage = message ?: "Auto-generated tag ${tagName}"
            sh "git tag -a ${tagName} -m '${tagMessage}'"
            sh "git push origin ${tagName}"
            
            Logger.success("标签创建并推送成功: ${tagName}", "GitUtils")
        } catch (Exception e) {
            Logger.error("标签创建失败: ${e.message}", "GitUtils")
            throw e
        }
    }
}
