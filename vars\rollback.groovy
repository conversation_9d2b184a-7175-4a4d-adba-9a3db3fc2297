def call(Map params) {
    if (params.DEPLOY_TYPE == 'docker') {
        sh 'docker ps -q --filter "name=${params.containerName}" | xargs -r docker stop'
        sh 'docker ps -a -q --filter "name=${params.containerName}" | xargs -r docker rm'
        sh 'docker images --format "{{.Repository}}:{{.Tag}}" | grep ${params.imageName} | sort | tail -n 2 | head -n 1 | xargs -r docker run -d --name ${params.containerName}'
    } else if (params.DEPLOY_TYPE == 'docker-compose') {
        sh 'docker-compose down'
        sh 'docker-compose up -d'
    } else if (params.DEPLOY_TYPE == 'k8s') {
        sh 'kubectl rollout undo deployment/$(kubectl get deploy -o jsonpath="{.items[0].metadata.name}") -n ${params.k8sNamespace ?: "default"}'
    } else {
        echo '未知部署类型，无法回滚'
    }
} 